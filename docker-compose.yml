version: '3.8'

services:
  rx-system:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rx-system
    ports:
      - "80:80"
      - "9000:9000"  # Thêm port 8000 để đảm bảo cả hai port đều được ánh xạ
    volumes:
      - ./.env:/app/.env:ro
      - ./logs:/app/logs
    env_file:
      - .env
    environment:
      - HOST=0.0.0.0
      - PORT=80
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    extra_hosts:
      - "host.docker.internal:host-gateway"
