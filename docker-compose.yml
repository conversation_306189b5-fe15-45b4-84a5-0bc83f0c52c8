version: '3.8' # <PERSON><PERSON> dụng phiên bản docker-compose phù hợp
services:
  # Dịch vụ FastAPI
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastapi_api
    env_file:
      - .env
    environment:
      - ENVIRONMENT=production
      - MAX_WORKERS=4
      - WORKERS_PER_CORE=1
      - TIMEOUT=120
      - GRACEFUL_TIMEOUT=120
      - KEEP_ALIVE=5
      - LOG_LEVEL=info
      - SKIP_AUTH_FOR_TESTS=false
    ports:
      - "8000:8000"
    restart: unless-stopped
    volumes:
      - ./app:/app/app
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s


  # Dịch vụ Redis cho caching (tùy chọn)
  redis:
    image: redis:alpine
    container_name: fastapi_redis
    command: redis-server --appendonly yes --replica-read-only no
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  redis_data: # Đ<PERSON>nh nghĩa volume để lưu trữ dữ liệu Redis