# /app/db/async_database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
import logging

from ..middleware.config import settings

logger = logging.getLogger(__name__)

async_database_url = settings.DATABASE_URL.replace('postgresql://', 'postgresql+asyncpg://')

# Tạo async engine với các tùy chọn tối ưu cho production
async_engine = create_async_engine(
    async_database_url,
    echo=False,  # Đặt thành True để debug SQL queries
    pool_size=20,  # Số lượng kết nối tối đa trong pool
    max_overflow=10,  # Số lượng kết nối có thể vượt quá pool_size
    pool_timeout=30,  # Thời gian chờ kết nối từ pool (giây)
    pool_recycle=1800,  # T<PERSON>i sử dụng kết nối sau 30 phút
    pool_pre_ping=True,  # Kiểm tra kết nối trước khi sử dụng
)

# Tạo AsyncSessionLocal class để tạo các session database bất đồng bộ
from sqlalchemy.ext.asyncio import async_sessionmaker

AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    expire_on_commit=False,  # Tránh lỗi khi truy cập đối tượng sau khi commit
)

# Base class cho các model (được import từ models.py khi cần)
Base = declarative_base()

# Hàm dependency để lấy DB session trong các API endpoint
async def get_async_db():
    """
    Hàm dependency tạo ra một SQLAlchemy async session.
    Đảm bảo session luôn được đóng sau khi sử dụng.
    """
    async_session = AsyncSessionLocal()
    try:
        logger.debug("Creating new async database session")
        yield async_session
    finally:
        logger.debug("Closing async database session")
        await async_session.close()

# Tạo dependency cho FastAPI
from typing import AsyncGenerator

async def get_async_db_dependency() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency cho FastAPI để lấy async database session.
    Sử dụng với Depends(get_async_db_dependency).
    """
    async with AsyncSessionLocal() as session:
        logger.debug("Creating new async database session (dependency)")
        yield session
        logger.debug("Closing async database session (dependency)")

# Hàm để tạo bảng trong database (chạy một lần khi setup)
async def init_async_db():
    """
    Tạo tất cả các bảng trong database nếu chưa tồn tại.
    Chỉ sử dụng trong môi trường phát triển hoặc khi cần thiết.
    Trong production, nên sử dụng Alembic để migration.
    """
    # Import tất cả các model ở đây để Base biết về chúng
    from . import models
    _ = models  # Suppress unused import warning

    logger.info("Đang tạo các bảng trong cơ sở dữ liệu (async)...")
    try:
        # Tạo tất cả các bảng
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        logger.info("Tạo bảng trong cơ sở dữ liệu thành công (async).")
    except Exception as e:
        logger.error(f"Lỗi khi tạo bảng trong cơ sở dữ liệu (async): {e}")
        raise
