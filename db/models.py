# /app/db/models.py
from sqlalchemy import (
    Column, String, JSON, DateTime, Foreign<PERSON>ey, <PERSON><PERSON><PERSON>, Integer, Text
)
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime, timezone

# Import Base từ async_database.py
from .async_database import Base

# Model bảng users
class User(Base):
    __tablename__ = 'users'
    # Sử dụng UUID làm kh<PERSON>a ch<PERSON>h, tự động tạo giá trị mặc định
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    # Identifier duy nhất từ hệ thống xác thực bên ngoài
    identifier = Column(String, unique=True, nullable=False, index=True)
    # Tên hiển thị của user/project
    name = Column(String, nullable=True)
    # Thời gian tạo, sử dụng UTC
    createdAt = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    # Thờ<PERSON> gian cập nhật cuối cùng
    updatedAt = Column(DateTime(timezone=True), nullable=True, onupdate=lambda: datetime.now(timezone.utc))
    # Dữ liệu metadata dạng JSON, đảm bảo có giá trị mặc định là dict rỗng
    metadata_ = Column("metadata", JSON, nullable=False, default=dict, server_default="{}")

    # Quan hệ một-nhiều với Thread
    threads = relationship("Thread", back_populates="user", cascade="all, delete-orphan")

# Model bảng threads
class Thread(Base):
    __tablename__ = 'threads'
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    createdAt = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    name = Column(String, nullable=True) # Cho phép tên thread là null ban đầu
    # Khóa ngoại liên kết đến bảng users, tự động xóa thread nếu user bị xóa
    userId = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    # Lưu lại identifier của user để tiện truy vấn (tùy chọn)
    userIdentifier = Column(String, nullable=True, index=True)
    # Tags dạng JSON list, mặc định là list rỗng
    tags = Column(JSON, nullable=False, default=list, server_default="[]")
    metadata_ = Column("metadata", JSON, nullable=False, default=dict, server_default="{}")

    # Quan hệ nhiều-một với User
    user = relationship("User", back_populates="threads")
    # Quan hệ một-nhiều với Step, tự động xóa step nếu thread bị xóa
    steps = relationship("Step", back_populates="thread", cascade="all, delete-orphan", order_by="Step.createdAt") # Sắp xếp step theo thời gian tạo
    # Quan hệ một-nhiều với Element, tự động xóa element nếu thread bị xóa
    elements = relationship("Element", back_populates="thread", cascade="all, delete-orphan")
    # Quan hệ một-nhiều với Feedback, tự động xóa feedback nếu thread bị xóa
    feedbacks = relationship("Feedback", back_populates="thread", cascade="all, delete-orphan")


# Model bảng steps
class Step(Base):
    __tablename__ = 'steps'
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False) # Tên của step (vd: "User Message", "AI Response")
    type = Column(String, nullable=False, index=True) # Loại step (vd: "USER_MESSAGE", "AI_RESPONSE", "TOOL", "ERROR")
    # Khóa ngoại liên kết đến bảng threads, tự động xóa step nếu thread bị xóa
    threadId = Column(String, ForeignKey("threads.id", ondelete="CASCADE"), nullable=False, index=True)
    # ID của step cha (dùng để liên kết AI response với User message)
    parentId = Column(String, ForeignKey("steps.id"), nullable=True, index=True)
    # Các trường bạn đã định nghĩa
    streaming = Column(Boolean, nullable=False, default=False)
    waitForAnswer = Column(Boolean, nullable=True) # Cho phép null nếu không áp dụng
    isError = Column(Boolean, default=False)
    metadata_ = Column("metadata", JSON, nullable=False, default=dict, server_default="{}")
    tags = Column(JSON, nullable=False, default=list, server_default="[]")
    input = Column(Text, nullable=True) # Cho phép null (vd: AI response không có input trực tiếp)
    output = Column(Text, nullable=True) # Cho phép null (vd: User message không có output)
    createdAt = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    start = Column(DateTime(timezone=True), nullable=True) # Thời gian bắt đầu xử lý step
    end = Column(DateTime(timezone=True), nullable=True) # Thời gian kết thúc xử lý step
    defaultOpen = Column(Boolean, default=False)
    generation = Column(JSON, nullable=True, default=dict) # Lưu thông tin generation (nếu có)
    showInput = Column(Text, nullable=True) # Có thể là input đã được format để hiển thị
    language = Column(String, nullable=True)
    indent = Column(Integer, nullable=True) # Thụt lề (cho hiển thị UI)

    # Quan hệ nhiều-một với Thread
    thread = relationship("Thread", back_populates="steps")
    # Quan hệ một-nhiều với chính nó (cho parent/child steps)
    # Sửa tên backref để tránh trùng lặp nếu có quan hệ khác tên 'parent'
    children = relationship("Step", backref="parent_step", remote_side=[id])


# Model bảng elements (dùng để lưu file, ảnh,... liên quan đến chat)
class Element(Base):
    __tablename__ = 'elements'
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    # Khóa ngoại liên kết đến bảng threads
    threadId = Column(String, ForeignKey("threads.id", ondelete="CASCADE"), nullable=False, index=True)
    type = Column(String, nullable=True) # Loại element (vd: "image", "file", "audio")
    url = Column(String, nullable=True) # URL của element (nếu lưu trữ bên ngoài)
    chainlitKey = Column(String, nullable=True) # ID tham chiếu từ Chainlit (nếu có)
    name = Column(String, nullable=False) # Tên hiển thị của element
    display = Column(String, nullable=True) # Cách hiển thị ('inline', 'side', 'page')
    objectKey = Column(String, nullable=True) # Key của object nếu lưu trên S3/Cloud Storage
    size = Column(String, nullable=True) # Kích thước (vd: "1.2MB")
    page = Column(Integer, nullable=True) # Số trang (cho PDF)
    language = Column(String, nullable=True) # Ngôn ngữ (cho code block)
    # ID của Step mà element này thuộc về (quan trọng để liên kết element với message cụ thể)
    forId = Column(String, ForeignKey("steps.id", ondelete="CASCADE"), nullable=True, index=True)
    mime = Column(String, nullable=True) # Kiểu MIME (vd: "image/png")
    props = Column(JSON, nullable=True, default=dict) # Các thuộc tính khác

    # Quan hệ nhiều-một với Thread
    thread = relationship("Thread", back_populates="elements")
    # Quan hệ nhiều-một với Step (optional, nếu element gắn với step cụ thể)
    step = relationship("Step") # Không cần back_populates nếu không cần truy cập elements từ step

# Model bảng feedbacks
class Feedback(Base):
    __tablename__ = 'feedbacks'
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    # ID của Step được feedback
    forId = Column(String, ForeignKey("steps.id", ondelete="CASCADE"), nullable=False, index=True)
    # Khóa ngoại liên kết đến bảng threads
    threadId = Column(String, ForeignKey("threads.id", ondelete="CASCADE"), nullable=False, index=True)
    # Giá trị feedback (vd: 1 cho tích cực, -1 cho tiêu cực, 0 cho trung tính)
    value = Column(Integer, nullable=False)
    comment = Column(Text, nullable=True) # Bình luận chi tiết
    createdAt = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc)) # Thêm thời gian tạo feedback

    # Quan hệ nhiều-một với Thread
    thread = relationship("Thread", back_populates="feedbacks")
    # Quan hệ nhiều-một với Step
    step = relationship("Step") # Không cần back_populates nếu không cần truy cập feedbacks từ step
