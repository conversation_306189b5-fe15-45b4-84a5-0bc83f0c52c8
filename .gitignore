# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
**/__pycache__/
**/*.py[cod]
**/*$py.class

# Virtual Environment
.venv/
venv/
ENV/
env/

# Environment variables
.env
.env.*

# Logs
logs/
*.log

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
.chainlit/translations/*
.files/
rAgent/ragents/.files/
cline_docs/

# Ignore all files in the .chainlit directory
# except for the .gitkeep file
# This will ensure that the .gitkeep file is tracked by Git