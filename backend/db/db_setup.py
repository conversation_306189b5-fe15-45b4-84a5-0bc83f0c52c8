import os
import sys
import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from .models import Base
from dotenv import load_dotenv
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import psycopg2

# Load environment variables từ file .env
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stdout
)
logger = logging.getLogger('db_setup')

# Determine if running in Docker
def is_running_in_docker():
    return os.path.exists('/.dockerenv')

# PostgreSQL connection details
POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "postgres")

# Adjust host based on environment
if is_running_in_docker() and os.getenv("POSTGRES_HOST") == "localhost":
    logger.info("Running in Docker and POSTGRES_HOST is localhost")
    logger.info("Converting localhost to host.docker.internal for Docker networking")
    POSTGRES_HOST = "host.docker.internal"
else:
    POSTGRES_HOST = os.getenv("POSTGRES_HOST", "localhost")

POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
POSTGRES_DB = os.getenv("POSTGRES_DB", "ragent-rome")

logger.info(f"Trying to connect to PostgreSQL at {POSTGRES_HOST}:{POSTGRES_PORT}...")

DATABASE_URL = f"postgresql://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

# Create database engine
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_database():
    """Tạo cơ sở dữ liệu nếu chưa tồn tại."""
    try:
        # Kết nối tới PostgreSQL mà không chỉ định cơ sở dữ liệu
        connection = psycopg2.connect(
            dbname="postgres",
            user=POSTGRES_USER,
            password=POSTGRES_PASSWORD,
            host=POSTGRES_HOST,
            port=POSTGRES_PORT
        )
        connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = connection.cursor()

        # Kiểm tra và tạo cơ sở dữ liệu nếu chưa tồn tại
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{POSTGRES_DB}'")
        exists = cursor.fetchone()
        if not exists:
            cursor.execute(f"CREATE DATABASE {POSTGRES_DB}")
            logger.info(f"Database '{POSTGRES_DB}' created successfully.")
        else:
            logger.info(f"Database '{POSTGRES_DB}' already exists.")

        cursor.close()
        connection.close()
    except Exception as e:
        logger.error(f"Failed to create database: {e}", exc_info=True)
        sys.exit(1)

def setup_database():
    try:
        logger.info(f"Connecting to database: {DATABASE_URL}")
        # Tạo bảng dựa trên các model trong models.py
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully.")
    except Exception as e:
        logger.error(f"Database setup failed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    create_database()
    setup_database()
