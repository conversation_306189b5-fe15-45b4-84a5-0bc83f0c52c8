#!/usr/bin/env python3
"""Test script for Redis fix"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, '.')

async def test_redis_fix():
    """Test the Redis fix"""
    print("🔄 Testing Redis fix...")
    
    try:
        # Import after path setup
        from app.middleware.cache import setup_redis, get_cache, set_cache
        
        print("✅ Redis modules imported successfully")
        
        # Test setup with timeout
        print("🔄 Testing Redis setup...")
        await asyncio.wait_for(setup_redis(), timeout=10.0)
        print("✅ Redis setup completed (or gracefully failed)")
        
        # Test cache operations
        print("🔄 Testing cache operations...")
        
        # Test set cache
        result = await set_cache("test_key", "test_value", expire=60)
        print(f"✅ Set cache result: {result}")
        
        # Test get cache
        value = await get_cache("test_key")
        print(f"✅ Get cache result: {value}")
        
        print("✅ All Redis tests completed successfully!")
        
    except asyncio.TimeoutError:
        print("⚠️ Redis test timed out - this is expected if <PERSON><PERSON> is not running")
    except Exception as e:
        print(f"⚠️ Redis test failed: {e}")
        print("This is expected if <PERSON><PERSON> is not installed or running")

if __name__ == "__main__":
    asyncio.run(test_redis_fix())
