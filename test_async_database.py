#!/usr/bin/env python3
"""Test script for async_database.py"""

try:
    from db.async_database import AsyncSessionLocal, get_async_db_dependency
    print("✅ async_database imported successfully!")
    
    # Test AsyncSessionLocal creation
    session = AsyncSessionLocal()
    print(f"✅ AsyncSessionLocal created: {type(session)}")
    
    # Test dependency function exists
    print(f"✅ get_async_db_dependency function: {get_async_db_dependency}")
    
    print("✅ All async_database components working correctly!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
