FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    python3-dev \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*
# Copy the wheel file and requirements first for better caching
COPY library/ ./library/
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create a directory for logs
RUN mkdir -p logs

# Set environment variables
ENV HOST=0.0.0.0
ENV PORT=80
ENV PYTHONUNBUFFERED=1

# Expose the ports the app runs on
EXPOSE 80

# Install PostgreSQL client for health checks
RUN apt-get update && apt-get install -y postgresql-client && rm -rf /var/lib/apt/lists/*

# # Copy the entrypoint script
# COPY docker-entrypoint.sh /app/docker-entrypoint.sh

# # Make the entrypoint script executable
# RUN chmod +x /app/docker-entrypoint.sh

# # Set the entrypoint
# ENTRYPOINT ["/app/docker-entrypoint.sh"]

# Command to run the application
CMD ["python", "api_app.py"]