#!/usr/bin/env python3
"""Test script for DTOs"""

try:
    from dtos import UserCreate, ChatRequest, StepResponse
    print("✅ DTOs imported successfully!")
    
    # Test UserCreate
    user = UserCreate(identifier="test_user", name="Test User")
    print(f"✅ UserCreate: {user}")
    
    # Test ChatRequest
    chat_req = ChatRequest(thread_id="test_thread", message="Hello", agent_type="rx")
    print(f"✅ ChatRequest: {chat_req}")
    
    print("✅ All DTOs working correctly!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
