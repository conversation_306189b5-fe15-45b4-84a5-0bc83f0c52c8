# API Test Suite

This directory contains test scripts for testing the FastAPI backend endpoints.

## Test Files

- `test_thread_endpoints.py`: Tests for thread creation, retrieval, updating, and deletion
- `test_chat_endpoint.py`: Tests for sending chat messages and retrieving responses
- `test_feedback_endpoint.py`: Tests for creating feedback on AI responses
- `test_user_endpoint.py`: Tests for user information retrieval
- `conftest.py`: Shared pytest fixtures for all tests
- `run_tests.py`: Script to run all tests in sequence

## Authentication

The tests automatically handle authentication by:

1. Making a login request to the authentication endpoint:
   ```
   POST https://api.runx.app/api/v1/auth/signin
   ```

2. Using the credentials defined in `conftest.py`:
   ```python
   login_data = {
       "email": "<EMAIL>",
       "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>@1998"
   }
   ```

3. Caching the token in a file called `token_cache.json` to avoid making unnecessary login requests

You don't need to manually provide a token - the tests will handle authentication automatically.

## Running Tests

### Prerequisites

1. Make sure the API server is running on `http://localhost:8000`
2. Install the required packages:

```bash
pip install -r tests/requirements-test.txt
```

### Running All Tests

To run all tests in sequence:

```bash
python tests/run_tests.py
```

### Running Individual Test Files

To run a specific test file:

```bash
pytest tests/test_thread_endpoints.py -v
```

### Running a Specific Test

To run a specific test function:

```bash
pytest tests/test_thread_endpoints.py::test_create_thread -v
```

## Test Structure

Each test file follows a similar structure:

1. Setup: Create necessary resources for testing
2. Test functions: Individual test cases
3. Teardown: Clean up resources after testing

The `conftest.py` file provides shared fixtures that can be used across multiple test files.

## Notes

- Tests are designed to be run in sequence within each file
- Some tests depend on the success of previous tests in the same file
- The `run_tests.py` script runs the test files in a specific order to ensure dependencies are met

## Token Handling

The API now supports improved token handling:

1. The API can handle tokens with or without the "Bearer" prefix
2. If a token already contains "Bearer" or "Bear" prefix, the system will automatically clean it up
3. The `test_user_endpoint.py` file includes a test for duplicate Bearer prefix handling
4. When using the Swagger UI, you should enter the token without the "Bearer" prefix in the Authorize dialog
