import requests
import pytest
import uuid
import time
from conftest import BASE_URL

# Test data for direct execution
test_thread_name = f"Chat Test Thread {uuid.uuid4()}"

# Fixture for creating a test thread
@pytest.fixture(scope="module")
def chat_test_thread(auth_headers):
    """Create a thread for testing chat functionality"""
    # Prepare data for creating a thread
    data = {
        "name": f"Chat Test Thread {uuid.uuid4()}",
        "tags": ["chat_test"],
        "metadata": {"source": "chat_api_test"}
    }

    # Send POST request to create thread
    response = requests.post(f"{BASE_URL}/threads/", json=data, headers=auth_headers)

    # Check if thread was created successfully
    if response.status_code == 201:
        thread_id = response.json()["id"]
        print(f"Created test thread with ID: {thread_id}")
    else:
        pytest.fail(f"Failed to create test thread. Status code: {response.status_code}")

    # Yield the thread ID for use in tests
    yield {"id": thread_id, "name": data["name"]}

    # Clean up by deleting the thread
    response = requests.delete(f"{BASE_URL}/threads/{thread_id}", headers=auth_headers)
    if response.status_code == 204:
        print(f"Deleted test thread with ID: {thread_id}")
    else:
        print(f"Warning: Failed to delete test thread. Status code: {response.status_code}")

# Fixture for storing the AI step ID
@pytest.fixture(scope="module")
def ai_step_id():
    return {"id": None}

def test_send_chat_message(chat_test_thread, auth_headers, ai_step_id):
    """Test sending a chat message and receiving a response"""
    thread_id = chat_test_thread["id"]

    # Prepare chat message data
    data = {
        "thread_id": thread_id,
        "message": "Hello, this is a test message. Can you respond with a short greeting?"
    }

    # Send POST request to chat endpoint
    response = requests.post(f"{BASE_URL}/chat", json=data, headers=auth_headers)

    # Assert response status code
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # Parse response JSON
    chat_response = response.json()

    # Assertions
    assert chat_response["thread_id"] == thread_id
    assert "ai_response" in chat_response
    assert len(chat_response["ai_response"]) > 0
    assert "user_step" in chat_response
    assert "ai_step" in chat_response

    # Save AI step ID for feedback test
    ai_step_id["id"] = chat_response["ai_step"]["id"]

    print(f"Received AI response: {chat_response['ai_response'][:50]}...")
    print(f"AI step ID: {ai_step_id['id']}")

def test_get_thread_steps(chat_test_thread, auth_headers):
    """Test getting all steps in a thread"""
    thread_id = chat_test_thread["id"]

    # Send GET request to get thread steps
    response = requests.get(f"{BASE_URL}/threads/{thread_id}/steps", headers=auth_headers)

    # Assert response status code
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # Parse response JSON
    steps = response.json()

    # Assertions
    assert isinstance(steps, list)
    assert len(steps) >= 2  # Should have at least user message and AI response

    # Check if steps contain expected types
    user_message_found = False
    ai_response_found = False

    for step in steps:
        if step["type"] == "USER_MESSAGE":
            user_message_found = True
        elif step["type"] == "AI_RESPONSE":
            ai_response_found = True

    assert user_message_found, "User message step not found"
    assert ai_response_found, "AI response step not found"

    print(f"Found {len(steps)} steps in the thread")

def test_multiple_messages(chat_test_thread, auth_headers):
    """Test sending multiple messages in the same thread"""
    thread_id = chat_test_thread["id"]

    # Đầu tiên, gửi tin nhắn đầu tiên để đảm bảo có ít nhất 2 bước
    first_message_data = {
        "thread_id": thread_id,
        "message": "Hello, this is a test message. Can you respond with a short greeting?"
    }

    # Gửi tin nhắn đầu tiên
    first_response = requests.post(f"{BASE_URL}/chat", json=first_message_data, headers=auth_headers)
    assert first_response.status_code == 200, f"Expected status code 200, got {first_response.status_code}"

    # Đợi một chút để đảm bảo tin nhắn đầu tiên được xử lý
    time.sleep(1)

    # Gửi tin nhắn thứ hai
    second_message_data = {
        "thread_id": thread_id,
        "message": "What's the weather like today?"
    }

    # Gửi tin nhắn thứ hai
    second_response = requests.post(f"{BASE_URL}/chat", json=second_message_data, headers=auth_headers)
    assert second_response.status_code == 200, f"Expected status code 200, got {second_response.status_code}"

    # Parse response JSON
    chat_response = second_response.json()

    # Assertions
    assert chat_response["thread_id"] == thread_id
    assert "ai_response" in chat_response
    assert len(chat_response["ai_response"]) > 0

    print(f"Received second AI response: {chat_response['ai_response'][:50]}...")

    # Đợi một chút để đảm bảo tất cả các bước được lưu
    time.sleep(1)

    # Get updated steps to verify both messages are in the thread
    steps_response = requests.get(f"{BASE_URL}/threads/{thread_id}/steps", headers=auth_headers)
    steps = steps_response.json()

    # In ra các bước để debug
    print(f"Steps found: {len(steps)}")
    for i, step in enumerate(steps):
        if step is not None:
            step_type = step.get('type', 'UNKNOWN')
            step_input = step.get('input', '')
            step_output = step.get('output', '')
            if step_output:
                step_output = step_output[:30]
            print(f"Step {i+1}: {step_type} - {step_input} - {step_output}")
        else:
            print(f"Step {i+1}: None")

    # Should now have at least 4 steps (2 user messages, 2 AI responses)
    assert len(steps) >= 4, f"Expected at least 4 steps, got {len(steps)}"

    print(f"Thread now has {len(steps)} steps after second message")

if __name__ == "__main__":
    """
    This section allows running the tests directly without pytest.
    It's useful for quick testing during development.
    """
    import sys
    from conftest import get_bearer_token

    # Get auth headers
    auth_headers = {
        "Authorization": f"Bearer {get_bearer_token()}",
        "Content-Type": "application/json"
    }

    # Create test thread
    data = {
        "name": test_thread_name,
        "tags": ["chat_test"],
        "metadata": {"source": "chat_api_test"}
    }

    # Send POST request to create thread
    response = requests.post(f"{BASE_URL}/threads/", json=data, headers=auth_headers)

    if response.status_code != 201:
        print(f"Failed to create test thread. Status code: {response.status_code}")
        sys.exit(1)

    thread_id = response.json()["id"]
    print(f"Created test thread with ID: {thread_id}")

    # Create a test thread object similar to the fixture
    test_thread = {"id": thread_id, "name": test_thread_name}

    # Create an AI step ID object similar to the fixture
    step_id = {"id": None}

    try:
        # Run tests
        test_send_chat_message(test_thread, auth_headers, step_id)
        test_get_thread_steps(test_thread, auth_headers)
        test_multiple_messages(test_thread, auth_headers)
        print("All chat endpoint tests passed!")
    finally:
        # Clean up
        response = requests.delete(f"{BASE_URL}/threads/{thread_id}", headers=auth_headers)
        if response.status_code == 204:
            print(f"Deleted test thread with ID: {thread_id}")
        else:
            print(f"Warning: Failed to delete test thread. Status code: {response.status_code}")
