import requests
import pytest
import uuid
import time
from conftest import BASE_URL

# Test data for direct execution
test_thread_name = f"Feedback Test Thread {uuid.uuid4()}"

# Fixture for creating a test thread and AI step for feedback tests
@pytest.fixture(scope="module")
def feedback_test_data(auth_headers):
    """Create a thread and generate a chat message for testing feedback"""
    # Create a thread
    thread_data = {
        "name": f"Feedback Test Thread {uuid.uuid4()}",
        "tags": ["feedback_test"],
        "metadata": {"source": "feedback_api_test"}
    }

    thread_response = requests.post(f"{BASE_URL}/threads/", json=thread_data, headers=auth_headers)

    if thread_response.status_code != 201:
        pytest.fail(f"Failed to create test thread. Status code: {thread_response.status_code}")

    thread_id = thread_response.json()["id"]
    print(f"Created test thread with ID: {thread_id}")

    # Send a chat message to get an AI response
    chat_data = {
        "thread_id": thread_id,
        "message": "Hello, this is a test message for feedback testing. Please respond."
    }

    chat_response = requests.post(f"{BASE_URL}/chat", json=chat_data, headers=auth_headers)

    if chat_response.status_code != 200:
        pytest.fail(f"Failed to create test AI response. Status code: {chat_response.status_code}")

    step_id = chat_response.json()["ai_step"]["id"]
    print(f"Created test AI step with ID: {step_id}")

    # Yield the test data for use in tests
    yield {
        "thread_id": thread_id,
        "step_id": step_id
    }

    # Clean up by deleting the thread
    response = requests.delete(f"{BASE_URL}/threads/{thread_id}", headers=auth_headers)
    if response.status_code == 204:
        print(f"Deleted test thread with ID: {thread_id}")
    else:
        print(f"Warning: Failed to delete test thread. Status code: {response.status_code}")

def test_create_positive_feedback(feedback_test_data, auth_headers):
    """Test creating positive feedback for an AI response"""
    step_id = feedback_test_data["step_id"]
    thread_id = feedback_test_data["thread_id"]

    # Prepare feedback data (positive)
    data = {
        "forId": step_id,
        "value": 1,  # Positive feedback
        "comment": "This response was helpful!"
    }

    # Send POST request to create feedback
    response = requests.post(f"{BASE_URL}/feedbacks/", json=data, headers=auth_headers)

    # Assert response status code
    assert response.status_code == 201, f"Expected status code 201, got {response.status_code}"

    # Parse response JSON
    feedback = response.json()

    # Assertions
    assert feedback["forId"] == step_id
    assert feedback["value"] == 1
    assert feedback["comment"] == "This response was helpful!"
    assert "id" in feedback
    assert "threadId" in feedback
    assert feedback["threadId"] == thread_id

    print(f"Created positive feedback with ID: {feedback['id']}")

def test_create_negative_feedback(feedback_test_data, auth_headers):
    """Test creating negative feedback for an AI response"""
    step_id = feedback_test_data["step_id"]
    thread_id = feedback_test_data["thread_id"]

    # Prepare feedback data (negative)
    data = {
        "forId": step_id,
        "value": -1,  # Negative feedback
        "comment": "This response could be improved."
    }

    # Send POST request to create feedback
    response = requests.post(f"{BASE_URL}/feedbacks/", json=data, headers=auth_headers)

    # Assert response status code
    assert response.status_code == 201, f"Expected status code 201, got {response.status_code}"

    # Parse response JSON
    feedback = response.json()

    # Assertions
    assert feedback["forId"] == step_id
    assert feedback["value"] == -1
    assert feedback["comment"] == "This response could be improved."
    assert "id" in feedback
    assert "threadId" in feedback
    assert feedback["threadId"] == thread_id

    print(f"Created negative feedback with ID: {feedback['id']}")

def test_create_feedback_without_comment(feedback_test_data, auth_headers):
    """Test creating feedback without a comment"""
    step_id = feedback_test_data["step_id"]
    thread_id = feedback_test_data["thread_id"]

    # Prepare feedback data (without comment)
    data = {
        "forId": step_id,
        "value": 1  # Positive feedback
    }

    # Send POST request to create feedback
    response = requests.post(f"{BASE_URL}/feedbacks/", json=data, headers=auth_headers)

    # Assert response status code
    assert response.status_code == 201, f"Expected status code 201, got {response.status_code}"

    # Parse response JSON
    feedback = response.json()

    # Assertions
    assert feedback["forId"] == step_id
    assert feedback["value"] == 1
    assert feedback["comment"] is None
    assert "id" in feedback
    assert "threadId" in feedback
    assert feedback["threadId"] == thread_id

    print(f"Created feedback without comment with ID: {feedback['id']}")

def test_create_feedback_invalid_step(auth_headers):
    """Test creating feedback for a non-existent step"""
    # Prepare feedback data with invalid step ID
    data = {
        "forId": "non_existent_step_id",
        "value": 1,
        "comment": "This should fail"
    }

    # Send POST request to create feedback
    response = requests.post(f"{BASE_URL}/feedbacks/", json=data, headers=auth_headers)

    # Assert response status code (should be 404 Not Found)
    assert response.status_code == 404, f"Expected status code 404, got {response.status_code}"

    print("Successfully detected invalid step ID")

if __name__ == "__main__":
    """
    This section allows running the tests directly without pytest.
    It's useful for quick testing during development.
    """
    import sys
    from conftest import get_bearer_token

    # Get auth headers
    auth_headers = {
        "Authorization": f"Bearer {get_bearer_token()}",
        "Content-Type": "application/json"
    }

    # Create a thread
    thread_data = {
        "name": test_thread_name,
        "tags": ["feedback_test"],
        "metadata": {"source": "feedback_api_test"}
    }

    thread_response = requests.post(f"{BASE_URL}/threads/", json=thread_data, headers=auth_headers)

    if thread_response.status_code != 201:
        print(f"Failed to create test thread. Status code: {thread_response.status_code}")
        sys.exit(1)

    thread_id = thread_response.json()["id"]
    print(f"Created test thread with ID: {thread_id}")

    # Send a chat message to get an AI response
    chat_data = {
        "thread_id": thread_id,
        "message": "Hello, this is a test message for feedback testing. Please respond."
    }

    chat_response = requests.post(f"{BASE_URL}/chat", json=chat_data, headers=auth_headers)

    if chat_response.status_code != 200:
        print(f"Failed to create test AI response. Status code: {chat_response.status_code}")
        # Clean up thread before exiting
        requests.delete(f"{BASE_URL}/threads/{thread_id}", headers=auth_headers)
        sys.exit(1)

    step_id = chat_response.json()["ai_step"]["id"]
    print(f"Created test AI step with ID: {step_id}")

    # Create test data object similar to the fixture
    test_data = {
        "thread_id": thread_id,
        "step_id": step_id
    }

    try:
        # Run tests
        test_create_positive_feedback(test_data, auth_headers)
        test_create_negative_feedback(test_data, auth_headers)
        test_create_feedback_without_comment(test_data, auth_headers)
        test_create_feedback_invalid_step(auth_headers)
        print("All feedback endpoint tests passed!")
    finally:
        # Clean up
        response = requests.delete(f"{BASE_URL}/threads/{thread_id}", headers=auth_headers)
        if response.status_code == 204:
            print(f"Deleted test thread with ID: {thread_id}")
        else:
            print(f"Warning: Failed to delete test thread. Status code: {response.status_code}")
