#!/usr/bin/env python3
"""
Script to run all API tests in sequence.
"""
import os
import sys
import subprocess
import time
import requests
from conftest import get_bearer_token, BASE_URL

# Base URL for the API
API_BASE_URL = "http://localhost:8000"

def check_api_availability():
    """Check if the API is available before running tests"""
    try:
        # Try the root endpoint first
        response = requests.get(f"{API_BASE_URL}/")
        if response.status_code == 200:
            print("✅ API is available (root endpoint)")
            return True

        # If root fails, try the docs endpoint
        response = requests.get(f"{API_BASE_URL}/api/docs")
        if response.status_code == 200:
            print("✅ API is available (docs endpoint)")
            return True
    except requests.exceptions.ConnectionError:
        pass

    print("❌ API is not available. Please make sure the API server is running.")
    return False

def check_authentication():
    """Check if authentication is working"""
    token = get_bearer_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.get(f"{BASE_URL}/users/me", headers=headers)
        if response.status_code == 200:
            user = response.json()
            print(f"✅ Authentication successful. Logged in as: {user.get('identifier', 'Unknown user')}")
            return True
        else:
            print(f"❌ Authentication failed. Status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking authentication: {e}")
        return False

def run_test_file(test_file):
    """Run a specific test file and return success status"""
    print(f"\n{'='*80}")
    print(f"Running tests in {test_file}")
    print(f"{'='*80}")

    result = subprocess.run(["python", "-m", "pytest", test_file, "-v"])

    if result.returncode == 0:
        print(f"✅ Tests in {test_file} passed")
        return True
    else:
        print(f"❌ Tests in {test_file} failed")
        return False

def main():
    """Main function to run all tests"""
    # Check if API is available
    if not check_api_availability():
        sys.exit(1)

    # Check if authentication is working
    if not check_authentication():
        print("⚠️ Authentication check failed. Tests may not work correctly.")
        proceed = input("Do you want to proceed anyway? (y/n): ")
        if proceed.lower() != 'y':
            sys.exit(1)

    # List of test files to run in order
    test_files = [
        "test_user_endpoint.py",
        "test_thread_endpoints.py",
        "test_chat_endpoint.py",
        "test_feedback_endpoint.py"
    ]

    # Run each test file
    all_passed = True
    for test_file in test_files:
        if not run_test_file(test_file):
            all_passed = False

    # Print summary
    print("\n" + "="*80)
    if all_passed:
        print("✅ All tests passed successfully!")
    else:
        print("❌ Some tests failed. Please check the output above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
