import pytest
import requests
import uuid
import json
import os

# Base URL for the API
BASE_URL = "http://localhost:8000/api/v1"
AUTH_URL = "https://api.runx.app/api/v1/auth/signin"

# Function to get bearer token
def get_bearer_token():
    # Check if token is cached in a file and still valid
    token_file = os.path.join(os.path.dirname(__file__), "token_cache.json")

    # Try to read cached token first
    if os.path.exists(token_file):
        try:
            with open(token_file, 'r') as f:
                token_data = json.load(f)
                if token_data.get('token'):
                    print("Using cached token")
                    return token_data['token']
        except Exception as e:
            print(f"Error reading cached token: {e}")

    # If no cached token or error, get a new one
    print("Getting new token from API...")

    # Login credentials
    login_data = {
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON><PERSON><PERSON><PERSON>@1998"
    }

    try:
        # Make login request
        response = requests.post(
            AUTH_URL,
            headers={"Content-Type": "application/json"},
            json=login_data
        )

        # Check if login was successful
        if response.status_code == 201:
            response_data = response.json()
            token = response_data.get('token')

            # Cache the token
            try:
                with open(token_file, 'w') as f:
                    json.dump({"token": token}, f)
            except Exception as e:
                print(f"Error caching token: {e}")

            return token
        else:
            print(f"Login failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"Error during login request: {e}")
        return None

# Get token at module level
TOKEN = get_bearer_token()

# Headers with authentication
@pytest.fixture(scope="module")
def auth_headers():
    # If TOKEN is None, skip the test
    if TOKEN is None:
        pytest.skip("Could not obtain authentication token")

    # Return token without 'Bearer ' prefix as our new implementation handles this automatically
    return {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }

@pytest.fixture(scope="module")
def create_test_thread(auth_headers):
    """Fixture to create a test thread and clean it up after the test"""
    thread_name = f"Test Thread {uuid.uuid4()}"
    # Use auth_headers fixture instead of creating new headers

    # Create thread
    data = {
        "name": thread_name,
        "tags": ["test", "fixture"],
        "metadata": {"source": "pytest_fixture"}
    }

    response = requests.post(f"{BASE_URL}/threads/", json=data, headers=auth_headers)

    if response.status_code != 201:
        pytest.fail(f"Failed to create test thread. Status code: {response.status_code}")

    thread_id = response.json()["id"]

    # Yield the thread ID and name to the test
    yield {"id": thread_id, "name": thread_name}

    # Clean up after the test
    delete_response = requests.delete(f"{BASE_URL}/threads/{thread_id}", headers=auth_headers)

    if delete_response.status_code != 204:
        print(f"Warning: Failed to delete test thread {thread_id}. Status code: {delete_response.status_code}")

@pytest.fixture(scope="module")
def create_chat_message(create_test_thread, auth_headers):
    """Fixture to create a chat message and return the step ID"""
    thread_id = create_test_thread["id"]

    # Send a chat message
    data = {
        "thread_id": thread_id,
        "message": "This is a test message from pytest fixture."
    }

    response = requests.post(f"{BASE_URL}/chat", json=data, headers=auth_headers)

    if response.status_code != 200:
        pytest.fail(f"Failed to create test chat message. Status code: {response.status_code}")

    chat_response = response.json()

    # Return both user and AI step IDs
    return {
        "thread_id": thread_id,
        "user_step_id": chat_response["user_step"]["id"],
        "ai_step_id": chat_response["ai_step"]["id"],
        "ai_response": chat_response["ai_response"]
    }
