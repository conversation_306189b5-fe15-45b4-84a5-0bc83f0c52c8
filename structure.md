app/
├── controllers/          # X<PERSON> lý các request HTTP
│   ├── __init__.py
│   ├── routes.py         # Định nghĩa các route (đổi tên từ endpoints.py)
│   ├── thread_routes.py  # Các route liên quan đến thread
│   ├── chat_routes.py    # Các route liên quan đến chat
│   └── user_routes.py    # Các route liên quan đến user
├── models/               # Chứa các model database
│   ├── __init__.py
│   └── models.py
├── schemas/              # Định nghĩa các Pydantic schemas
│   ├── __init__.py
│   ├── thread_schemas.py
│   ├── chat_schemas.py
│   └── user_schemas.py
├── services/             # Các service xử lý logic nghiệp vụ
│   ├── __init__.py
│   ├── chatgpt_service.py
│   ├── rivalz_service.py
│   └── utils.py
├── db/                   # Xử lý kết nối và thao tác database
│   ├── __init__.py
│   ├── async_database.py
│   └── async_crud.py
├── middleware/           # Middleware cho ứng dụng
│   ├── __init__.py
│   ├── async_security.py
│   ├── cache.py
│   └── config.py
├── utils/                # Các hàm tiện ích
│   ├── __init__.py
│   └── helpers.py
├── main.py               # Entry point
└── __init__.py