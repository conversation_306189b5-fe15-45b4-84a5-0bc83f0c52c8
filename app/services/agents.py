import sys
import os
# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "./..")))

from rAgent.agents import AgentCallbacks
from rAgent.agents import OpenAIAgent, OpenAIAgentOptions
from rAgent.ragents import RXAgent, RXAgentOptions
import asyncio
from rAgent.classifiers import OpenAIClassifier, OpenAIClassifierOptions
from rAgent.utils import Logger
from rAgent.ragents.RXRivalz_team_V1 import RXTeamSupervisorRivalz, RXTeamSupervisorRivalzOptions
from rAgent.ragents.RX_team import RXTeamSupervisor, RXTeamSupervisorOptions
from config import settings
OPENAI_API_KEY = settings.OPENAI_API_KEY
OPENAI_MODEL = settings.CHATGPT_MODEL
DEEP_INFRA_KEY = settings.DEEP_INFRA_API_KEY
DEEP_INFRA_URL = settings.DEEP_INFRA_URL
DEEP_INFRA_MODEL= settings.DEEP_INFRA_MODEL
auth_key = settings.RIVAL_AUTH_KEY if settings.RIVAL_AUTH_KEY else ""
RIVALZ_API_URL = settings.RIVAL_URL

def create_tech_agent():
    options = OpenAIAgentOptions(
            name="Tech Agent",
            description="Specializes in technology areas including software development, hardware, AI, cybersecurity, blockchain, cloud computing, emerging tech innovations, and pricing/costs related to technology products and services.",
            api_key=OPENAI_API_KEY,
            model=OPENAI_MODEL,
            # base_url=DEEP_INFRA_URL,
            streaming=True,
            inference_config={
                'maxTokens': 500,
                'temperature': 0.5,
                'topP': 0.8,
                'stopSequences': []
            },
        )
    return OpenAIAgent(options)


def create_default_agent():
    options = OpenAIAgentOptions(
            name="General Agent",
            description="""Default agent for handling general inquiries. Can provide information on a wide range of topics and answer common questions. If no specialized agent is available, this agent will handle the conversation.
            You may be asked about the context related about rAgent of ROME in rival (They are the AI Agents that can interact with resources), here are the context about ROME and rAgent:
            Integration with AI Agents and Identity Verification
Recognizing the growing role of AI agents in decentralized ecosystems, Rome is designed to support and interact with AI agents as first-class participants. The platform's mission is to abstract the complexities of the physical and computational world for AI agents, enabling them to access and utilize resources efficiently.
Romes, the identity verification system, allows users to provide compelling verification through different means. Identity verification plays a crucial role in the reputation system, ensuring that resource provisioning is secure and trustworthy. It also helps limit the amount of resources a single user can supply, preventing botting and sybil attacks, which is essential for maintaining the integrity of interactions involving AI agents.
Users register via Rome, validate themselves, provide resources and receive tokenized versions of those resources in the form of staked tokens.
Additionally, Rome introduces resource mapping for users with verified identities, enabling them to submit information about their technology assets, IoT devices, vehicles, energy resources, for Rome Phase 2 integration.
            """,
            api_key=OPENAI_API_KEY,
            model=OPENAI_MODEL,
            # base_url=DEEP_INFRA_URL,
            streaming=False,
            inference_config={
                'maxTokens': 500,
                'temperature': 0.5,
                'topP': 0.8,
                'stopSequences': []
            },
        )
    return OpenAIAgent(options)

def create_travel_agent():
    options = OpenAIAgentOptions(
            name="Travel Agent",
            description="Experienced Travel Agent sought to create unforgettable journeys for clients. Responsibilities include crafting personalized itineraries, booking flights, accommodations, and activities, and providing expert travel advice. Must have excellent communication skills, destination knowledge, and ability to manage multiple bookings. Proficiency in travel booking systems and a passion for customer service required",
            api_key=OPENAI_API_KEY,
            model=OPENAI_MODEL,
            # base_url=DEEP_INFRA_URL,
            streaming=True,
            inference_config={
                'maxTokens': 500,
                'temperature': 0.5,
                'topP': 0.8,
                'stopSequences': []
            },
        )
    return OpenAIAgent(options)

def create_health_agent():
    options = OpenAIAgentOptions(
            name="Health Agent",
            description="Specializes in health and wellness, including nutrition, fitness, mental health, and disease prevention. Provides personalized health advice, creates wellness plans, and offers resources for self-care. Must have a strong understanding of human anatomy, physiology, and medical terminology. Proficiency in health coaching techniques and a commitment to promoting overall well-being required.",
            api_key=OPENAI_API_KEY,
            model=OPENAI_MODEL,
            # base_url=DEEP_INFRA_URL,
            streaming=True,
            inference_config={
                'maxTokens': 500,
                'temperature': 0.5,
                'topP': 0.8,
                'stopSequences': []
            },
        )
    return OpenAIAgent(options)

def create_X_agent():
    options = RXAgentOptions(
            name="X Agent",
            description="Specializes in twitter (X) areas, you can interact with this agent to post tweets and reply to tweets.",
            api_key=OPENAI_API_KEY,
            model=OPENAI_MODEL,
            # base_url=DEEP_INFRA_URL,
            xaccesstoken="U3I4ZVlmSHA1MzhDdmdJb3E3SVMxaHVWYnBPcTZTcWV6RW85aktJUFdUaWxiOjE3Mzk3MzIxODQzNDU6MTowOmF0OjE",
            inference_config={
                'maxTokens': 500,
                'temperature': 0.5,
                'topP': 0.8,
                'stopSequences': []
            },
        )
    return RXAgent(options)

async def create_rx_supervisor(storage = None, num_agents=99, project_id=""):
    lead_agent = OpenAIAgent(OpenAIAgentOptions(
        api_key=OPENAI_API_KEY,
        name="SupervisorAgent",
        description=("You a supervisor agent specialized in coordinating X_Agents for social media management (on X). Your role is to orchestrate and oversee the posting and replying activities on Twitter/X platform. You delegate tasks to X_Agents, monitor their performance, and ensure all social media interactions are executed efficiently."
                     " You coordinate when and how tweets should be posted or replied to, maintaining a strategic approach to social media engagement."),
        model=OPENAI_MODEL,
        # base_url=DEEP_INFRA_URL,
        inference_config={
                'maxTokens': 500,
                'temperature': 0.5,
                'topP': 0.8,
                'stopSequences': []
            },
        # share_global_memory=True,
        # streaming=True,
    ))
    supervisor = RXTeamSupervisorRivalz(
        RXTeamSupervisorRivalzOptions(
            type="selection",
            name="RX_Team_Supervisor",
            description="Manages team of social media posting agents",
            lead_agent=lead_agent,  # Your configured lead agent
            authen_key=auth_key,
            api_url=RIVALZ_API_URL,
            project_id=project_id,
            trace=True,
            storage = storage,
            share_global_memory=True,
            number_of_agents=num_agents,
            # Advanced options (optional)
            custom_prompt_templates={},
            content_formatting={}
        )
    )
    
    # Initialize supervisor (now only fetches team information, doesn't create agents)
    await supervisor.initialize()
    
    # Log team statistics
    team_stats = supervisor.get_team_stats()
    Logger.info(f"Team initialized with {team_stats['rx_available']} RX agents available")
    
    return supervisor

def create_classifier():
    Logger.info("Creating classifier")
    classifier = OpenAIClassifier(OpenAIClassifierOptions(
    api_key=OPENAI_API_KEY,
    model_id=OPENAI_MODEL,
    # base_url=DEEP_INFRA_URL,
    inference_config={
                    'maxTokens': 500,
                    'temperature': 0.6,
                    'topP': 0.8,
                    'stopSequences': []
                }
    ))
    return classifier



