from openai import Async<PERSON>penA<PERSON>
from typing import List, Dict, Optional
import logging

from ..config import settings
from ..db import models

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the OpenAI client with the API key from settings
try:
    client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    logger.info("AsyncOpenAI client initialized successfully.")
except Exception as e:
    logger.error(f"Failed to initialize AsyncOpenAI client: {e}", exc_info=True)
    client = None

# System prompt for Helpful Agent


HELPFUL_AGENT_SYSTEM_PROMPT = [{
    "role": "system",
    "content": """
        You are Helpful Agent, a versatile AI assistant designed to help users with a wide range of questions and tasks.

## 🔹 Requirements & Guidelines:
1️⃣ **Provide Helpful & Detailed Answers**:
   - Answer questions clearly, comprehensively, and in a structured manner.
   - Give practical, actionable advice when appropriate.
   - Include examples, step-by-step instructions, or useful tips when relevant.
   - Aim for thorough responses that truly help the user understand the topic.

2️⃣ **Use Web Search When Needed**:
   - For questions requiring current information, recent data, or factual accuracy, use web search tools to provide up-to-date information.
   - Prioritize reliable, authoritative sources when searching for information.
   - Always verify important facts and provide accurate, well-sourced responses.

3️⃣ **Be Honest About Limitations**:
   - Clearly state when you don't know something or when a question is outside your capabilities.
   - For specialized professional advice (medical, legal, financial), recommend consulting appropriate experts.
   - Acknowledge uncertainty and avoid making definitive claims about complex or sensitive topics.

4️⃣ **Maintain Quality Standards**:
   - Provide well-organized, easy-to-understand responses.
   - Use appropriate formatting (lists, headings, etc.) to improve readability.
   - Be concise yet comprehensive - avoid unnecessary verbosity while ensuring completeness.

5️⃣ **Be Respectful & Professional**:
   - Maintain a helpful, friendly, and professional tone.
   - Respect user privacy and don't request personal information.
   - Be inclusive and considerate in all interactions.

6️⃣ **Encourage Learning & Growth**:
   - Help users understand concepts rather than just providing answers.
   - Suggest additional resources or related topics when appropriate.
   - Encourage critical thinking and independent problem-solving.

## 🔹 Examples:
✅ "How do I learn a new programming language effectively?"
✅ "What are the steps to start a small business?"
✅ "Can you explain how machine learning works?"
✅ "Help me plan a budget for my monthly expenses."

❌ "Provide specific medical diagnosis" → "I can't provide medical diagnoses. Please consult a healthcare professional."
❌ "Give legal advice for my court case" → "For legal matters, please consult with a qualified attorney."
    """
}]

def format_history_for_prompt(steps: List[models.Step]) -> List[Dict[str, str]]:
    # Start with the system prompt in correct format
    prompt_history = [HELPFUL_AGENT_SYSTEM_PROMPT[0]]  # Get the first (and only) dict from the list

    # Add conversation history
    for step in steps:
        step_type = getattr(step, 'type', '')
        step_input = getattr(step, 'input', None)
        step_output = getattr(step, 'output', None)

        if step_type == "USER_MESSAGE" and step_input is not None:
            input_content = str(step_input).strip()
            if input_content:
                prompt_history.append({"role": "user", "content": input_content})
        elif step_type == "AI_RESPONSE" and step_output is not None:
            output_content = str(step_output).strip()
            if output_content:
                prompt_history.append({"role": "assistant", "content": output_content})

    return prompt_history

async def get_chatgpt_response(prompt_messages: List[Dict[str, str]]) -> Optional[Dict]:
    """
    Call the OpenAI API to get a response from the ChatGPT model.

    Args:
        prompt_messages: List of messages in the format expected by the OpenAI API.

    Returns:
        The complete response from the API as a dictionary, or None if there was an error.
    """
    if not client:
        logger.error("OpenAI client not initialized. Cannot call API.")
        return None

    try:
        logger.info(f"Calling OpenAI API with model: {settings.CHATGPT_MODEL}")
        response = await client.chat.completions.create(
            model=settings.CHATGPT_MODEL,
            messages=prompt_messages,  # type: ignore
            temperature=0.7,
            max_tokens=4096,
            timeout=30,
        )
        logger.info("Received response from OpenAI API.")

        # Convert the response object to a dictionary
        response_dict = response.model_dump()
        return response_dict

    except Exception as e:
        logger.error(f"Error calling OpenAI API: {e}", exc_info=True)
        return None

def extract_content_from_response(response: Optional[Dict]) -> Optional[str]:
    """
    Extract the text content from the OpenAI API response (new format).

    Args:
        response: The response dictionary from the OpenAI API.

    Returns:
        The extracted text content, or None if extraction failed.
    """
    if not response:
        return None
    try:
        # Extract content from the response dictionary
        if isinstance(response, dict) and "choices" in response:
            choices = response.get("choices", [])
            if choices and len(choices) > 0:
                message = choices[0].get("message", {})
                content = message.get("content")
                if content:
                    return content.strip()

        logger.warning(f"Could not extract content from response: {response}")
        return None
    except (IndexError, KeyError, AttributeError) as e:
        logger.error(f"Error extracting content: {e}. Response: {response}", exc_info=True)
        return None

async def test_openai_api():
    """
    Test function to verify the OpenAI API connection and response.

    Returns:
        The response from the API as a string, or an error message.
    """
    test_message = [
        HELPFUL_AGENT_SYSTEM_PROMPT[0],  # Get the dict from the list
        {"role": "user", "content": "Hello, can you help me with a general question?"}
    ]

    try:
        response_dict = await get_chatgpt_response(test_message)
        content = extract_content_from_response(response_dict)

        if content:
            logger.info(f"API test successful. Response: {content}")
            return f"API test successful. Response: {content}"
        else:
            logger.error("API test failed: Could not extract content from response")
            return "API test failed: Could not extract content from response"
    except Exception as e:
        error_msg = f"API test failed with error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return error_msg
