"""
Utility functions for the services module.
"""

import re
from typing import Optional

def clean_text(extracted_text: str) -> Optional[str]:
    """
    Clean the extracted text by removing tags and ensuring only one instance of [AgentName].
    
    Args:
        extracted_text: The text to clean
        
    Returns:
        The cleaned text, or None if there's no content besides the agent name
    """
    # Remove all occurrences of <startagent> and <endagent>
    cleaned_text = re.sub(r'<startagent>|<endagent>', '', extracted_text)
    
    # Ensure only one instance of [AgentName]
    agent_name_match = re.search(r'\[([^\]]+)\]', cleaned_text)
    
    if agent_name_match:
        agent_name = agent_name_match.group(0)
        # Check if the cleaned text only contains the agent name
        remaining_text = re.sub(r'\[([^\]]+)\]', '', cleaned_text).strip()
        
        # If there's no content besides the agent name, return None
        if not remaining_text:
            return None
            
        # Otherwise, format with agent name at the beginning
        cleaned_text = re.sub(r'\[([^\]]+)\]', '', cleaned_text)
        cleaned_text = agent_name + " " + cleaned_text
        
    return cleaned_text.strip()


from typing import Tuple, Optional

def parse_agent_response(text: str) -> Tuple[Optional[str], str]:
    """
    Tách tên agent và nội dung từ một chuỗi có định dạng "[Agent_Name]: content" hoặc "[Agent_Name] content".
    Trả về một tuple (agent_name, content).
    Nếu không tìm thấy định dạng, agent_name sẽ là None và content là chuỗi gốc đã strip.
    """
    if not isinstance(text, str):
        return None, str(text)

    # Làm dấu hai chấm thành tùy chọn với :?
    match = re.match(r'^\s*\[([A-Za-z0-9_.\s-]+)\]:?\s*(.*)', text.strip(), re.DOTALL)
    if match:
        agent_name = match.group(1).strip()
        content = match.group(2).strip()
        return agent_name, text.strip()
    else:
        return None, text.strip()