class TestSetupRedis(unittest.TestCase):
    """Test cases for the setup_redis function."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.loop = asyncio.get_event_loop()
        
    def test_setup_redis_not_available(self):
        """Test setup_redis when Redis library is not available."""
        self.loop.run_until_complete(self._test_setup_redis_not_available())
    
    async def _test_setup_redis_not_available(self):
        """Async implementation of test_setup_redis_not_available."""
        with patch('app.middleware.cache.REDIS_AVAILABLE', False):
            with patch('app.middleware.cache.logger') as mock_logger:
                await setup_redis()
                mock_logger.warning.assert_called_once_with(
                    "Redis library not installed. Cache and rate limiting will be disabled."
                )
    
    def test_setup_redis_client_init_fails(self):
        """Test setup_redis when Redis client initialization fails."""
        self.loop.run_until_complete(self._test_setup_redis_client_init_fails())
    
    async def _test_setup_redis_client_init_fails(self):
        """Async implementation of test_setup_redis_client_init_fails."""
        with patch('app.middleware.cache.REDIS_AVAILABLE', True):
            with patch('app.middleware.cache.init_redis_client', return_value=None) as mock_init:
                with patch('app.middleware.cache.logger') as mock_logger:
                    await setup_redis()
                    mock_init.assert_called_once()
                    mock_logger.warning.assert_called_with(
                        "Could not connect to Redis. Cache and rate limiting will be disabled."
                    )
    
    def test_setup_redis_connection_timeout(self):
        """Test setup_redis when Redis connection times out."""
        self.loop.run_until_complete(self._test_setup_redis_connection_timeout())
    
    async def _test_setup_redis_connection_timeout(self):
        """Async implementation of test_setup_redis_connection_timeout."""
        mock_redis_instance = MagicMock()
        
        with patch('app.middleware.cache.REDIS_AVAILABLE', True):
            with patch('app.middleware.cache.init_redis_client', return_value=mock_redis_instance):
                with patch('asyncio.wait_for', side_effect=asyncio.TimeoutError):
                    with patch('app.middleware.cache.logger') as mock_logger:
                        await setup_redis()
                        mock_logger.warning.assert_called_with(
                            "Redis connection timed out. Cache and rate limiting will be disabled."
                        )
    
    def test_setup_redis_connection_error(self):
        """Test setup_redis when Redis connection fails with general exception."""
        self.loop.run_until_complete(self._test_setup_redis_connection_error())
    
    async def _test_setup_redis_connection_error(self):
        """Async implementation of test_setup_redis_connection_error."""
        mock_redis_instance = MagicMock()
        test_error = Exception("Connection failed")
        
        with patch('app.middleware.cache.REDIS_AVAILABLE', True):
            with patch('app.middleware.cache.init_redis_client', return_value=mock_redis_instance):
                with patch('asyncio.wait_for', side_effect=test_error):
                    with patch('app.middleware.cache.logger') as mock_logger:
                        await setup_redis()
                        mock_logger.warning.assert_called_with(
                            f"Redis connection failed: {test_error}. Cache and rate limiting will be disabled."
                        )
    
    def test_setup_redis_ping_timeout(self):
        """Test setup_redis when ping operation times out."""
        self.loop.run_until_complete(self._test_setup_redis_ping_timeout())
    
    async def _test_setup_redis_ping_timeout(self):
        """Async implementation of test_setup_redis_ping_timeout."""
        mock_redis_instance = MagicMock()
        
        with patch('app.middleware.cache.REDIS_AVAILABLE', True):
            with patch('app.middleware.cache.init_redis_client', return_value=mock_redis_instance):
                # First call succeeds (init_redis_client), second call times out (ping)
                with patch('asyncio.wait_for', side_effect=[mock_redis_instance, asyncio.TimeoutError]):
                    with patch('app.middleware.cache.logger') as mock_logger:
                        await setup_redis()
                        mock_logger.warning.assert_called_with(
                            "Redis connection timed out. Cache and rate limiting will be disabled."
                        )
    
    def test_setup_redis_limiter_init_fails(self):
        """Test setup_redis when FastAPI Limiter initialization fails."""
        self.loop.run_until_complete(self._test_setup_redis_limiter_init_fails())
    
    async def _test_setup_redis_limiter_init_fails(self):
        """Async implementation of test_setup_redis_limiter_init_fails."""
        mock_redis_instance = MagicMock()
        limiter_error = Exception("Limiter init failed")
        
        with patch('app.middleware.cache.REDIS_AVAILABLE', True):
            with patch('app.middleware.cache.init_redis_client', return_value=mock_redis_instance):
                with patch('asyncio.wait_for', return_value=True):  # Ping succeeds
                    with patch('app.middleware.cache._safe_limiter_call', side_effect=limiter_error):
                        with patch('app.middleware.cache.logger') as mock_logger:
                            await setup_redis()
                            mock_logger.error.assert_called_with(
                                f"Failed to initialize FastAPI Limiter: {limiter_error}"
                            )
    
    def test_setup_redis_success(self):
        """Test successful Redis setup."""
        self.loop.run_until_complete(self._test_setup_redis_success())
    
    async def _test_setup_redis_success(self):
        """Async implementation of test_setup_redis_success."""
        mock_redis_instance = MagicMock()
        
        with patch('app.middleware.cache.REDIS_AVAILABLE', True):
            with patch('app.middleware.cache.REDIS_URL', "redis://localhost:6379/0"):
                with patch('app.middleware.cache.init_redis_client', return_value=mock_redis_instance):
                    with patch('asyncio.wait_for', return_value=True):  # Both init and ping succeed
                        with patch('app.middleware.cache._safe_limiter_call', return_value=None):
                            with patch('app.middleware.cache.logger') as mock_logger:
                                await setup_redis()
                                
                                # Verify success messages
                                mock_logger.info.assert_any_call(
                                    "Successfully connected to Redis at redis://localhost:6379/0"
                                )
                                mock_logger.info.assert_any_call(