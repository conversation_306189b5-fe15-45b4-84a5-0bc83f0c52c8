# /app/core/middleware.py
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
import time
import logging
import json
from typing import Callable, Dict, Any, Optional
import uuid

# Thử import các thư viện tù<PERSON> chọn
try:
    from fastapi_limiter import Fast<PERSON>ILimiter
    from fastapi_limiter.depends import RateLimiter
    RATE_LIMIT_AVAILABLE = True
except ImportError:
    RATE_LIMIT_AVAILABLE = False
    # Tạo một decorator giả để tránh lỗi khi không có thư viện
    def RateLimiter(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware để ghi log các request và response.
    """
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        # Ghi log request
        logger.info(f"Request {request_id} started: {request.method} {request.url.path}")

        # Log request headers for debugging (safely hide sensitive info)
        headers_dict = dict(request.headers.items())
        if "project-id" in headers_dict:
            project_id = headers_dict["project-id"]
            # Mask part of the project ID for security
            if len(project_id) > 10:
                headers_dict["project-id"] = f"{project_id[:5]}...{project_id[-5:]}"

        logger.debug(f"Request {request_id} headers: {headers_dict}")

        # Đo thời gian xử lý
        start_time = time.time()

        try:
            # Xử lý request
            response = await call_next(request)

            # Tính thời gian xử lý
            process_time = time.time() - start_time

            # Thêm header X-Process-Time và X-Request-ID
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Request-ID"] = request_id

            # Ghi log response
            logger.info(
                f"Request {request_id} completed: {request.method} {request.url.path} - "
                f"Status: {response.status_code} - Time: {process_time:.4f}s"
            )

            return response
        except Exception as e:
            # Tính thời gian xử lý
            process_time = time.time() - start_time

            # Ghi log lỗi
            logger.error(
                f"Request {request_id} failed: {request.method} {request.url.path} - "
                f"Error: {str(e)} - Time: {process_time:.4f}s",
                exc_info=True
            )

            # Trả về lỗi 500 nếu có exception
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error", "request_id": request_id}
            )

def setup_middlewares(app: FastAPI) -> None:
    """
    Thiết lập tất cả các middleware cho ứng dụng.
    """
    # Thêm CORS middleware
    origins = [
        "http://localhost",
        "http://localhost:3000",  # React dev server
        "http://localhost:8000",  # FastAPI dev server
        "http://localhost:5173",  # Vite/Vue dev server
        # Thêm domain production của bạn
        # "https://your-frontend-domain.com",
    ]

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Trong production, nên chỉ định cụ thể origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Thêm middleware ghi log request
    app.add_middleware(RequestLoggingMiddleware)

    logger.info("All middlewares have been set up")

# Decorator cho rate limiting
def rate_limit(
    calls: int = 100,
    period: int = 60,
    callback: Optional[Callable] = None
):
    """
    Decorator để áp dụng rate limiting cho các endpoint.

    Args:
        calls: Số lượng request tối đa trong khoảng thời gian
        period: Khoảng thời gian (giây)
        callback: Hàm callback khi rate limit bị vượt quá
    """
    if not RATE_LIMIT_AVAILABLE:
        # Nếu không có thư viện rate limiting, trả về một decorator giả
        def dummy_decorator(func):
            return func
        return dummy_decorator
    else:
        # Nếu có thư viện rate limiting, sử dụng Depends để áp dụng đúng cách
        from fastapi import Depends
        return Depends(RateLimiter(times=calls, seconds=period))
