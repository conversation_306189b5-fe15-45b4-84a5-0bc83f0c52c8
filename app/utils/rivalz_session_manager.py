"""
Session manager for Rivalz agents.

This module provides a session manager that maintains RivalzAgent instances
for each user based on project_id. It ensures that:
1. Each user gets a specific RivalzAgent instance based on their project_id
2. Inactive sessions are cleaned up after a period of inactivity
3. Resources are properly managed for production use
"""

import os
import time
import asyncio
import logging
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
import threading
from rAgent.storage import InMemoryChatStorage
from ..services.rivalz_agent import RivalzAgent

# Configure logging
logger = logging.getLogger(__name__)

class RivalzSessionManager:
    """
    Manages RivalzAgent instances for different projects.
    
    This class ensures that each project gets its own RivalzAgent instance,
    and that inactive sessions are cleaned up after a period of inactivity.
    """
    
    def __init__(self, cleanup_interval: int = 3600, session_timeout: int = 7200):
        """
        Initialize the session manager.
        
        Args:
            cleanup_interval (int): Interval in seconds between cleanup runs (default: 1 hour)
            session_timeout (int): Time in seconds after which an inactive session is removed (default: 2 hours)
        """
        self.agents: Dict[str, RivalzAgent] = {}
        self.last_active: Dict[str, float] = {}
        self.cleanup_interval = cleanup_interval
        self.session_timeout = session_timeout
        self.lock = threading.RLock()
        
        # Start the cleanup thread
        self._start_cleanup_thread()
    
    async def get_or_create_agent(self, project_id: str) -> RivalzAgent:
        """
        Get an existing agent for the project or create a new one if it doesn't exist.
        
        Args:
            project_id (str): The project identifier
            
        Returns:
            RivalzAgent: The agent instance for the project
        """
        with self.lock:
            # Check if we already have an agent for this project
            if project_id in self.agents:
                logger.info(f"Using existing agent for project {project_id}")
                self.update_last_active(project_id)
                return self.agents[project_id]
            
            # Create a new agent for this project
            logger.info(f"Creating new agent for project {project_id}")
            shared_storage = InMemoryChatStorage()
            
            # Use the factory method to create and initialize the agent
            agent = await RivalzAgent.create(project_id, shared_storage)
            
            # Store the agent and update last active timestamp
            self.agents[project_id] = agent
            self.update_last_active(project_id)
            
            return agent
    
    def update_last_active(self, project_id: str) -> None:
        """
        Update the last active timestamp for a project.
        
        Args:
            project_id (str): The project identifier
        """
        with self.lock:
            self.last_active[project_id] = time.time()
    
    def _start_cleanup_thread(self) -> None:
        """Start a background thread to periodically clean up inactive sessions."""
        def cleanup_task():
            while True:
                try:
                    self._cleanup_inactive_sessions()
                except Exception as e:
                    logger.error(f"Error in cleanup task: {e}", exc_info=True)
                
                # Sleep for the cleanup interval
                time.sleep(self.cleanup_interval)
        
        # Start the cleanup thread as a daemon so it doesn't prevent the program from exiting
        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
        cleanup_thread.start()
        logger.info(f"Started session cleanup thread with interval {self.cleanup_interval}s and timeout {self.session_timeout}s")
    
    def _cleanup_inactive_sessions(self) -> None:
        """Clean up inactive sessions."""
        current_time = time.time()
        projects_to_remove = []
        
        with self.lock:
            # Find inactive projects
            for project_id, last_active in self.last_active.items():
                if current_time - last_active > self.session_timeout:
                    projects_to_remove.append(project_id)
            
            # Remove inactive projects
            for project_id in projects_to_remove:
                if project_id in self.agents:
                    logger.info(f"Removing inactive agent for project {project_id}")
                    del self.agents[project_id]
                    del self.last_active[project_id]
        
        if projects_to_remove:
            logger.info(f"Cleaned up {len(projects_to_remove)} inactive sessions")
    
    def get_active_sessions_count(self) -> int:
        """
        Get the number of active sessions.
        
        Returns:
            int: The number of active sessions
        """
        with self.lock:
            return len(self.agents)

# Create a singleton instance of the session manager
rivalz_session_manager = RivalzSessionManager()
