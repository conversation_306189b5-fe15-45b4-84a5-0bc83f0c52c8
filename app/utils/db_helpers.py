"""
Database utility functions.
"""

import json
import datetime
from typing import Dict, Any
from sqlalchemy import MetaData
import logging

logger = logging.getLogger(__name__)

def sqlalchemy_to_dict(obj) -> Dict[str, Any]:
    """
    Convert SQLAlchemy model to dict, handling special fields.
    
    Args:
        obj: SQLAlchemy model instance
        
    Returns:
        Dict representation of the model
    """
    result = {}
    
    for key in obj.__dict__:
        if key.startswith('_'):
            continue
            
        value = getattr(obj, key)
        
        # Handle datetime objects
        if isinstance(value, datetime.datetime):
            result[key] = value.isoformat()
        # Handle JSON/dict fields
        elif isinstance(value, dict):
            result[key] = value
        # Handle list fields
        elif isinstance(value, list):
            result[key] = value
        # Handle None values
        elif value is None:
            result[key] = None
        # Handle other types
        else:
            try:
                # Try to serialize to JSON to check if it's serializable
                json.dumps(value)
                result[key] = value
            except (<PERSON><PERSON><PERSON><PERSON>, ValueError):
                # If not serializable, convert to string
                result[key] = str(value)
    
    return result
