"""
Utility functions for the application.
"""

import re
import logging
from typing import Optional, Dict, Any, Tuple

# Configure logging
logger = logging.getLogger(__name__)

def clean_text(extracted_text: str) -> Optional[str]:
    """
    Clean the extracted text by removing tags and ensuring only one instance of [AgentName].
    
    Args:
        extracted_text: The text to clean
        
    Returns:
        The cleaned text, or None if there's no content besides the agent name
    """
    # Remove all occurrences of <startagent> and <endagent>
    cleaned_text = re.sub(r'<startagent>|<endagent>', '', extracted_text)
    
    # Ensure only one instance of [AgentName]
    agent_name_match = re.search(r'\[([^\]]+)\]', cleaned_text)
    
    if agent_name_match:
        agent_name = agent_name_match.group(0)
        # Check if the cleaned text only contains the agent name
        remaining_text = re.sub(r'\[([^\]]+)\]', '', cleaned_text).strip()
        
        # If there's no content besides the agent name, return None
        if not remaining_text:
            return None
            
        # Otherwise, format with agent name at the beginning
        cleaned_text = re.sub(r'\[([^\]]+)\]', '', cleaned_text)
        cleaned_text = agent_name + " " + cleaned_text
        
    return cleaned_text.strip()

def extract_content_from_response_dict(response: Optional[Dict[str, Any]]) -> Optional[str]:
    """
    Extract the content text from a response dictionary.
    
    Args:
        response: Response dictionary from get_rivalz_response
        
    Returns:
        The extracted text content, or None if extraction failed
    """
    if not response:
        return None
    
    try:
        # Lấy raw_content từ response
        raw_content = response.get("raw_content", "")
        
        if raw_content:
            # Extract messages between <startagent> and <endagent>
            import re
            
            # Tìm tất cả các đoạn text nằm giữa <startagent> và <endagent>
            extracted_texts = re.findall(r'<\\?startagent>(.*?)<\\?endagent>', raw_content, re.DOTALL)
            
            # Nếu không tìm thấy, thử mẫu khác với escaped backslashes
            if not extracted_texts:
                extracted_texts = re.findall(r'<\\startagent>(.*?)<\\endagent>', raw_content, re.DOTALL)
            
            logger.info(f"Extraction result: {len(extracted_texts)} texts found")
            
            if extracted_texts:
                # Nếu tìm thấy các đoạn text, xử lý từng đoạn
                cleaned_texts = []
                for extracted_text in extracted_texts:
                    cleaned = clean_text(extracted_text)
                    if cleaned:
                        cleaned_texts.append(cleaned)
                
                if cleaned_texts:
                    # Kết hợp các đoạn text đã được làm sạch
                    return "\n\n".join(cleaned_texts)
            else:
                # Nếu không tìm thấy thẻ <startagent>, trả về raw_content
                return raw_content.strip()
        
        logger.warning(f"Could not extract content from response")
        return None
    except Exception as e:
        logger.error(f"Error extracting content: {e}", exc_info=True)
        return None

def parse_agent_response(text: str) -> Tuple[Optional[str], str]:
    """
    Tách tên agent và nội dung từ một chuỗi có định dạng "[Agent_Name]: content" hoặc "[Agent_Name] content".
    Trả về một tuple (agent_name, content).
    Nếu không tìm thấy định dạng, agent_name sẽ là None và content là chuỗi gốc đã strip.
    """
    if not isinstance(text, str):
        return None, str(text)

    # Làm dấu hai chấm thành tùy chọn với :?
    match = re.match(r'^\s*\[([A-Za-z0-9_.\s-]+)\]:?\s*(.*)', text.strip(), re.DOTALL)
    if match:
        agent_name = match.group(1).strip()
        content = match.group(2).strip()
        return agent_name, text.strip()
    else:
        return None, text.strip()
