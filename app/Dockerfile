FROM python:3.11-slim

WORKDIR /app

# Cài đặt các gói phụ thuộc hệ thống
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    curl \
    netcat-traditional \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Cài đặt các gói Python
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir pydantic-settings>=2.0.0

# Thêm Gunicorn và Uvicorn (đã có trong requirements.txt nhưng đảm bảo chắc chắn)
RUN pip install --no-cache-dir gunicorn uvicorn[standard]

# Sao chép mã nguồn ứng dụng
COPY . .

# Thiết lập biến môi trường
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Expose cổng
EXPOSE 8000

# Cấ<PERSON> quyền thực thi cho entrypoint.sh
COPY entrypoint.sh .
RUN chmod +x entrypoint.sh

# Sử dụng entrypoint.sh để khởi tạo database và khởi động ứng dụng
ENTRYPOINT ["./entrypoint.sh"]
CMD ["gunicorn", "app.main:app", "-c", "gunicorn_conf.py"]
