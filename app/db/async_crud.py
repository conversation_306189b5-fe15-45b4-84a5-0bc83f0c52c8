# /app/db/async_crud.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc, update, delete
from typing import List, Optional, Dict, Any
import logging

from . import models
from ..controllers import schemas

logger = logging.getLogger(__name__)

# --- User CRUD ---
async def get_user(db: AsyncSession, user_id: str) -> Optional[models.User]:
    """Lấy user bằng ID nội bộ (async)."""
    result = await db.execute(select(models.User).filter(models.User.id == user_id))
    return result.scalars().first()

async def get_user_by_identifier(db: AsyncSession, identifier: str) -> Optional[models.User]:
    """Lấy user bằng identifier (ID từ hệ thống bên ngoài) (async)."""
    result = await db.execute(select(models.User).filter(models.User.identifier == identifier))
    return result.scalars().first()

async def create_user(db: AsyncSession, user: schemas.UserCreate) -> models.User:
    """Tạo user mới trong DB cục bộ (async)."""
    db_user = models.User(
        identifier=user.identifier,
        name=user.name if hasattr(user, 'name') else None,
        metadata_=user.metadata if hasattr(user, 'metadata') else {}
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user

async def update_user(db: AsyncSession, user_id: str, user_data: dict) -> Optional[models.User]:
    """Cập nhật thông tin user (async)."""
    # Chuẩn bị dữ liệu cập nhật
    update_data = {}
    if 'name' in user_data:
        update_data["name"] = user_data["name"]
    if 'metadata' in user_data:
        update_data["metadata_"] = user_data["metadata"]
    
    if not update_data:
        return None  # Không có dữ liệu cập nhật

    # Thực hiện cập nhật
    await db.execute(
        update(models.User)
        .where(models.User.id == user_id)
        .values(**update_data)
    )
    
    await db.commit()
    
    # Lấy user đã cập nhật
    result = await db.execute(select(models.User).filter(models.User.id == user_id))
    return result.scalars().first()

# --- Thread CRUD ---
async def get_thread(db: AsyncSession, thread_id: str) -> Optional[models.Thread]:
    """Lấy thread bằng ID (async)."""
    result = await db.execute(select(models.Thread).filter(models.Thread.id == thread_id))
    return result.scalars().first()

async def get_threads_by_user(db: AsyncSession, user_id: str, skip: int = 0, limit: int = 100) -> List[models.Thread]:
    """Lấy danh sách các thread của một user (async)."""
    result = await db.execute(
        select(models.Thread)
        .filter(models.Thread.userId == user_id)
        .order_by(desc(models.Thread.createdAt))
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def create_thread(
    db: AsyncSession,
    thread_data: schemas.ThreadCreate,
    user_id: str,
    user_identifier: str
) -> models.Thread:
    """Tạo thread mới (async)."""
    db_thread = models.Thread(
        name=thread_data.name,
        userId=user_id,
        userIdentifier=user_identifier,
        tags=thread_data.tags if hasattr(thread_data, 'tags') and thread_data.tags else [],
        metadata_=thread_data.metadata if hasattr(thread_data, 'metadata') and thread_data.metadata else {}
    )
    db.add(db_thread)
    await db.commit()
    await db.refresh(db_thread)
    return db_thread

async def update_thread(
    db: AsyncSession,
    thread_id: str,
    thread_update: schemas.ThreadUpdate
) -> Optional[models.Thread]:
    """Cập nhật thông tin thread (async)."""
    # Chuẩn bị dữ liệu cập nhật
    update_data = {}
    if hasattr(thread_update, 'name') and thread_update.name is not None:
        update_data["name"] = thread_update.name
    if hasattr(thread_update, 'tags') and thread_update.tags is not None:
        update_data["tags"] = thread_update.tags
    if hasattr(thread_update, 'metadata') and thread_update.metadata is not None:
        update_data["metadata_"] = thread_update.metadata

    if not update_data:
        return None  # Không có dữ liệu cập nhật

    # Thực hiện cập nhật
    await db.execute(
        update(models.Thread)
        .where(models.Thread.id == thread_id)
        .values(**update_data)
    )
    await db.commit()

    # Lấy thread đã cập nhật
    return await get_thread(db, thread_id)

async def delete_thread(db: AsyncSession, thread_id: str) -> bool:
    """Xóa thread (async)."""
    result = await db.execute(
        delete(models.Thread)
        .where(models.Thread.id == thread_id)
    )
    await db.commit()
    return result.rowcount > 0

# --- Step CRUD ---
async def get_step(db: AsyncSession, step_id: str) -> Optional[models.Step]:
    """Lấy step bằng ID (async)."""
    result = await db.execute(select(models.Step).filter(models.Step.id == step_id))
    return result.scalars().first()

async def create_step(db: AsyncSession, step_data: schemas.StepCreate, thread_id: str) -> models.Step:
    """Tạo step mới (async)."""
    # Chuyển đổi từ Pydantic model sang dict
    step_dict = step_data.dict(exclude_unset=True)

    # Thêm threadId vào dict
    step_dict["threadId"] = thread_id

    # Đổi tên metadata nếu có
    if "metadata" in step_dict:
        step_dict["metadata_"] = step_dict.pop("metadata")

    # Tạo instance của Step
    db_step = models.Step(**step_dict)
    db.add(db_step)

    try:
        await db.commit()
        await db.refresh(db_step)
        return db_step
    except Exception as e:
        await db.rollback()
        raise e

async def get_steps_by_thread(db: AsyncSession, thread_id: str, skip: int = 0, limit: int = 100) -> List[models.Step]:
    """Lấy danh sách các step của một thread, sắp xếp theo thời gian tạo (async)."""
    try:
        # Đảm bảo session là mới
        await db.flush()

        # Thực hiện truy vấn
        result = await db.execute(
            select(models.Step)
            .filter(models.Step.threadId == thread_id)
            .order_by(models.Step.createdAt)
            .offset(skip)
            .limit(limit)
        )

        # Lấy tất cả kết quả
        steps = result.scalars().all()

        # Log số lượng steps tìm thấy
        print(f"Found {len(steps)} steps for thread {thread_id}")

        return steps
    except Exception as e:
        print(f"Error getting steps for thread {thread_id}: {e}")
        raise e

async def get_last_steps(db: AsyncSession, thread_id: str, limit: int = 20) -> List[models.Step]:
    """Lấy các step cuối cùng của một thread để làm context (async)."""
    try:
        # Đảm bảo session là mới
        await db.flush()

        # Thực hiện truy vấn
        result = await db.execute(
            select(models.Step)
            .filter(models.Step.threadId == thread_id)
            .order_by(desc(models.Step.createdAt))
            .limit(limit)
        )

        # Lấy tất cả kết quả
        steps = result.scalars().all()

        # Log số lượng steps tìm thấy
        print(f"Found {len(steps)} last steps for thread {thread_id}")

        # Đảo ngược lại để có thứ tự thời gian đúng
        return list(reversed(steps))
    except Exception as e:
        print(f"Error getting last steps for thread {thread_id}: {e}")
        raise e

# --- Feedback CRUD ---
async def create_feedback(
    db: AsyncSession,
    feedback_data: schemas.FeedbackCreate,
    thread_id: str
) -> models.Feedback:
    """Tạo feedback mới (async)."""
    db_feedback = models.Feedback(
        forId=feedback_data.forId,
        threadId=thread_id,
        value=feedback_data.value,
        comment=feedback_data.comment if hasattr(feedback_data, 'comment') else None
    )
    db.add(db_feedback)
    await db.commit()
    await db.refresh(db_feedback)
    return db_feedback

async def get_feedback_by_step_id(db: AsyncSession, step_id: str) -> Optional[models.Feedback]:
    """Lấy feedback cho một step cụ thể (async)."""
    result = await db.execute(select(models.Feedback).filter(models.Feedback.forId == step_id))
    return result.scalars().first()

async def get_feedback_value_by_step_id(db: AsyncSession, step_id: str) -> Dict[str, bool]:
    """
    Lấy giá trị feedback (like/dislike) cho một step cụ thể (async).

    Returns:
        Dict với 2 key 'like' và 'dislike' là boolean
    """
    feedback = await get_feedback_by_step_id(db, step_id)

    if not feedback:
        return {"like": False, "dislike": False}

    # Giá trị feedback: 1 = like, -1 = dislike
    return {
        "like": feedback.value == 1,
        "dislike": feedback.value == 0
    }
