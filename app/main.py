# /app/main.py
from fastapi import FastAP<PERSON>, HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.openapi.utils import get_openapi
from contextlib import asynccontextmanager
import logging
import time
import os
import sys
# Thêm project root vào Python path nếu chạy trực tiếp
if __name__ == "__main__":
    # Khi chạy trực tiếp main.py, thêm parent directory vào sys.path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
# <PERSON><PERSON><PERSON> thư viện tùy chọn - sẽ được import khi cần thiết
# from prometheus_fastapi_instrumentator import Instrumentator
# from pythonjsonlogger import jsonlogger

from .controllers import routes as v1_endpoints
from .db.async_database import async_engine, init_async_db # Async database
from .middleware.config import settings
from .services import chatgpt_service # Import to check client at startup
from .middleware.requestlogging import setup_middlewares
from .middleware.cache import setup_redis

# Cấu hình môi trường
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

# Cấu hình logging
log_level = getattr(logging, LOG_LEVEL)
log_handlers = [logging.StreamHandler()]  # Log to console

# Thêm file handler nếu là môi trường production
if ENVIRONMENT == "production":
    os.makedirs("logs", exist_ok=True)

    # Tạo rotating file handler để giới hạn kích thước file log
    try:
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            "logs/app.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
    except ImportError:
        file_handler = logging.FileHandler("logs/app.log")

    # Sử dụng JSON formatter nếu có thể
    try:
        from pythonjsonlogger import jsonlogger
        file_handler.setFormatter(
            jsonlogger.JsonFormatter("%(asctime)s %(name)s %(levelname)s %(message)s %(pathname)s %(lineno)s")
        )
    except ImportError:
        file_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )

    log_handlers.append(file_handler)

logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=log_handlers
)
logger = logging.getLogger(__name__)

# --- Lifespan Event Handler ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic
    logger.info(f"Application is starting in {ENVIRONMENT} environment...")

    # Kiểm tra kết nối database
    try:
        # Kiểm tra kết nối bất đồng bộ
        async with async_engine.begin() as _:
            logger.info("Asynchronous database connection successful.")

        # Khởi tạo bảng trong mọi môi trường khi khởi động
        try:
            await init_async_db()  # Asynchronous
            logger.info("Database tables initialized successfully.")
        except Exception as e:
            logger.error(f"Error initializing database tables: {e}", exc_info=True)
            # Không raise lỗi để ứng dụng vẫn có thể khởi động
    except Exception as e:
        logger.error(f"Database connection failed during startup: {e}", exc_info=True)

    # Kiểm tra OpenAI client
    if not chatgpt_service.client:
        logger.warning("OpenAI client is not configured. ChatGPT features will not be available.")
    else:
        logger.info("OpenAI client is configured.")

    # Khởi tạo Redis cho caching và rate limiting
    # Bọc trong try-except để tránh lỗi khi không có Redis
    try:
        await setup_redis(app)
    except Exception as e:
        logger.warning(f"Redis setup failed: {e}. Caching and rate limiting will not be available.")

    logger.info("Application startup completed.")

    yield  # Application runs here

    # Shutdown logic
    logger.info("Application is shutting down...")

    # Đóng kết nối async engine
    await async_engine.dispose()
    logger.info("Async database connections closed.")

    logger.info("Application shutdown completed.")

# --- Initialize FastAPI Application ---
app = FastAPI(
    title="Chatbot Backend API",
    description="API backend for chatbot application with ChatGPT integration and external authentication.",
    version="1.0.0",
    # Configure both default and custom paths to support both
    docs_url="/docs",
    redoc_url="/redoc",
    # Keep old path for backward compatibility
    # openapi_url="/api/openapi.json",
    lifespan=lifespan,
    # Add security scheme for Swagger UI
    openapi_tags=[
        {"name": "Users", "description": "Operations with users"},
        {"name": "Threads", "description": "Chat thread management"},
        {"name": "Chat", "description": "Chat with AI"},
        {"name": "Feedback", "description": "User feedback on AI responses"},
        {"name": "Root", "description": "Root endpoint for API status check"},
        {"name": "Health", "description": "Health check endpoints"},
    ]
)

# --- Thiết lập Middleware ---
# Sử dụng hàm setup_middlewares từ core.middleware
from fastapi.middleware.cors import CORSMiddleware
setup_middlewares(app)

# --- Thiết lập Monitoring ---
# Cấu hình Prometheus metrics (bỏ qua nếu không có thư viện)
if ENVIRONMENT == "production":
    try:
        from prometheus_fastapi_instrumentator import Instrumentator
        instrumentator = Instrumentator().instrument(app)
        instrumentator.expose(app, include_in_schema=False, should_gzip=True)
        logger.info("Prometheus metrics endpoint enabled at /metrics")
    except ImportError:
        logger.warning("prometheus_fastapi_instrumentator not installed. Metrics endpoint will not be available.")

# --- Exception Handlers ---
@app.exception_handler(HTTPException)
async def http_exception_error_handler(request: Request, exc: HTTPException): # Renamed function
    logger.warning(f"HTTP Exception: {exc.status_code} - Details: {exc.detail} - Path: {request.url.path}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
        headers=getattr(exc, "headers", None),
    )

from fastapi.exceptions import RequestValidationError
@app.exception_handler(RequestValidationError)
async def validation_exception_error_handler(request: Request, exc: RequestValidationError): # Renamed function
    errors = []
    for error in exc.errors():
        errors.append({"loc": error["loc"], "msg": error["msg"], "type": error["type"]})
    logger.warning(f"Validation Error: {errors} - Path: {request.url.path}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": "Data validation error", "errors": errors},
    )

# --- Security Scheme for Bearer Token ---
# Using security from app.core.security

# Function to customize OpenAPI schema with security
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Add security scheme to components
    openapi_schema["components"] = openapi_schema.get("components", {})
    openapi_schema["components"]["securitySchemes"] = {
        "projectIdHeader": {
            "type": "apiKey",
            "in": "header",
            "name": "project-id",
            "description": "Enter your project ID to authenticate with the API"
        }
    }

    # Add global security requirement for all endpoints except public ones
    for path in openapi_schema["paths"]:
        # Skip public endpoints and root endpoint
        if "/public/" in path or path == "/":
            continue

        # For each path, add security requirement to all operations
        for method in openapi_schema["paths"][path]:
            if str(method).lower() in ["get", "post", "put", "delete", "patch"]:
                openapi_schema["paths"][path][method]["security"] = [{"projectIdHeader": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# --- Include Routers ---
app.include_router(v1_endpoints.router, prefix="/api/v1")
app.include_router(v1_endpoints.public_router, prefix="/api/v1/public")

# Không cần import async_endpoints nữa vì đã tích hợp vào endpoints.py
logger.info("All endpoints registered successfully")

# Apply custom OpenAPI schema
app.openapi = custom_openapi

# --- Add route for Swagger UI at old path ---
from fastapi.responses import RedirectResponse

@app.get("/api/docs", include_in_schema=False)
async def custom_swagger_ui_redirect():
    return RedirectResponse(url="/docs")

# --- Health Check Endpoints ---
@app.get("/health", tags=["Health"], summary="Basic health check", include_in_schema=True)
async def health_check():
    """
    Basic health check endpoint that doesn't require authentication.
    Returns 200 OK if the API is running.
    """
    return {"status": "ok", "environment": ENVIRONMENT}

@app.get("/health/db", tags=["Health"], summary="Database health check", include_in_schema=True)
async def db_health_check():
    """
    Database health check endpoint.
    Checks if the database connection is working.
    """
    async_db_ok = False

    try:
        # Kiểm tra kết nối bất đồng bộ
        from .db.async_database import get_async_db_dependency

        # Sử dụng dependency để kiểm tra kết nối
        async for session in get_async_db_dependency():
            # Thực hiện một truy vấn đơn giản với text()
            from sqlalchemy import text
            await session.execute(text("SELECT 1"))
            async_db_ok = True
            break
    except Exception as e:
        logger.error(f"Async DB health check failed: {e}")
        async_db_ok = False

    result_status = "ok" if async_db_ok else "error"
    result_code = status.HTTP_200_OK if result_status == "ok" else status.HTTP_500_INTERNAL_SERVER_ERROR

    return JSONResponse(
        status_code=result_code,
        content={
            "status": result_status,
            "database": "ok" if async_db_ok else "error"
        }
    )

# --- Root Endpoint (Optional) ---
@app.get("/", tags=["Root"], summary="Check API status", include_in_schema=True)
async def read_root_endpoint():
    """
    Root endpoint that doesn't require authentication.
    Used to check if the API is running.
    """
    return {
        "message": "Welcome to Chatbot Backend API!",
        "version": "1.0.0",
        "environment": ENVIRONMENT,
        "docs_url": "/docs",
        "health_check": "/health"
    }

# --- Run application (for debug, usually run with uvicorn from CLI) ---
if __name__ == "__main__":
    import uvicorn
    logger.info("Starting Uvicorn server directly from main.py...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=ENVIRONMENT == "development",
        workers=1  # Chỉ sử dụng 1 worker trong development
    )
