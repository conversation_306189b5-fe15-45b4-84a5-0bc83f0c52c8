# Thay thế bằng thông tin kết nối PostgreSQL của bạn
DATABASE_URL="postgresql://runx:runx123@localhost:5432/runxchat"
# API Key của bạn từ OpenAI
OPENAI_API_KEY="yourkey"
# (<PERSON><PERSON><PERSON> chọn) Tên model ChatGPT muốn sử dụng
CHATGPT_MODEL="gpt-4o"
# (T<PERSON><PERSON> chọn) Số lượng message tối đa lấy làm context lịch sử chat
MAX_HISTORY_MESSAGES=20



# Environment (development, production)
ENVIRONMENT=development

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# PostgreSQL Configuration (for Docker Compose)
POSTGRES_USER=runx
POSTGRES_PASSWORD=runx123
POSTGRES_DB=runxchat

# Logging
LOG_LEVEL=INFO

# Worker Configuration (for Gun<PERSON>)
MAX_WORKERS=4
WORKERS_PER_CORE=1
TIMEOUT=120
GRACEFUL_TIMEOUT=120
KEEP_ALIVE=5

# Testing
SKIP_AUTH_FOR_TESTS=false
