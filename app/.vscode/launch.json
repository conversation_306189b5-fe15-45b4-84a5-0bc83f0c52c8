{"version": "0.2.0", "configurations": [{"name": "FastAPI Module Debug", "type": "python", "request": "launch", "module": "app.main", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "ENVIRONMENT": "development", "LOG_LEVEL": "DEBUG"}, "python": "/opt/miniconda3/envs/runx/bin/python", "justMyCode": false}, {"name": "FastAPI Direct Debug (with fix)", "type": "python", "request": "launch", "program": "${workspaceFolder}/app/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "ENVIRONMENT": "development", "LOG_LEVEL": "DEBUG"}, "python": "/opt/miniconda3/envs/runx/bin/python", "justMyCode": false}, {"name": "FastAPI Uvicorn Module", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "ENVIRONMENT": "development"}, "python": "/opt/miniconda3/envs/runx/bin/python"}]}