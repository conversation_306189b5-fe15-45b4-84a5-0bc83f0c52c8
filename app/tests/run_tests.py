"""
Test runner script.
"""

import unittest
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the test modules
from tests.test_rivalz_response_extraction import TestRivalzResponseExtraction

# Create a test suite
def create_test_suite():
    suite = unittest.TestSuite()
    
    # Add tests from TestRivalzResponseExtraction
    suite.addTest(unittest.makeSuite(TestRivalzResponseExtraction))
    
    return suite

if __name__ == "__main__":
    # Create the test suite
    suite = create_test_suite()
    
    # Run the tests
    runner = unittest.TextTestRunner()
    result = runner.run(suite)
    
    # Exit with appropriate status code
    sys.exit(not result.wasSuccessful())
