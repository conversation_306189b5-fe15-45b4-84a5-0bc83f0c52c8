"""
Test script for the Rivalz response extraction.

This script tests the functionality of the extract_content_from_response function
in the rivalz_service module.
"""

import unittest
import sys
import os
from typing import Optional, List, Dict, Any, Union, AsyncIterable # Ensure these are available

# Print current directory and Python path for debugging
print(f"Current directory: {os.getcwd()}")
print(f"Python path before: {sys.path}")

# Add the project root directory to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)
print(f"Added to path: {project_root}")
print(f"Python path after: {sys.path}")

try:
    from app.services.rivalz_service import extract_content_from_response
    print("Successfully imported rivalz_service.extract_content_from_response")
except ImportError as e:
    print(f"Import error for app.services.rivalz_service: {e}")
    sys.exit(1)

try:
    from rAgent.types import ConversationMessage, ParticipantRole 
    from rAgent.agents import AgentResponse, AgentProcessingResult 
    print("Successfully imported rAgent types and agents")
except ImportError as e:
    print(f"Import error for rAgent modules: {e}")
    print("Please ensure rAgent.types contains ConversationMessage, ParticipantRole")
    print("and rAgent.agents contains AgentResponse, AgentProcessingResult.")
    print("You might need to adjust the import paths or ensure __init__.py files are present.")
    sys.exit(1)


class TestRivalzResponseExtraction(unittest.TestCase):
    """
    Test cases for the extract_content_from_response function.
    """

    def test_extract_single_agent_response_with_tags(self):
        """
        Kiểm tra trích xuất khi có một khối <startagent>...<endagent>.
        Nội dung bên trong thẻ (bao gồm cả "[AgentName]:") sẽ được trả về.
        """
        # CORRECTED: Removed the trailing period from "[AgentA]: Hello from Agent A" inside the tag
        # and adjusted the surrounding text slightly for clarity if needed.
        raw_text_content = "Some text before <startagent>[AgentA]: Hello from Agent A<endagent> and some text after."
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}] 
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        # Expected result should not have the period if the input doesn't.
        expected_result = ["[AgentA]: Hello from Agent A"]
        actual_result = extract_content_from_response(agent_response)
        self.assertEqual(actual_result, expected_result)

    def test_extract_multiple_agent_responses_with_tags(self):
        """
        Kiểm tra trích xuất khi có nhiều khối <startagent>...<endagent>.
        Mỗi khối sẽ là một phần tử trong danh sách trả về.
        """
        raw_text_content = (
            "Intro. <startagent>[Supervisor]: Planning task.<endagent> "
            "Then, <startagent>[Worker1]: Executing part 1. Result: OK.<endagent> "
            "Finally, <startagent>[Worker2]: Executing part 2. Data: {...}<endagent> Concluding."
        )
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}]
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = [
            "[Supervisor]: Planning task.",
            "[Worker1]: Executing part 1. Result: OK.",
            "[Worker2]: Executing part 2. Data: {...}"
        ]
        actual_result = extract_content_from_response(agent_response)
        self.assertEqual(actual_result, expected_result)

    def test_extract_response_no_tags(self):
        """
        Kiểm tra khi không có thẻ <startagent>.
        Toàn bộ nội dung text (đã strip) sẽ được trả về trong một danh sách một phần tử.
        """
        raw_text_content = "  This is a simple response without any agent tags.  "
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}]
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = ["This is a simple response without any agent tags."]
        actual_result = extract_content_from_response(agent_response)
        self.assertEqual(actual_result, expected_result)

    def test_extract_response_with_tags_and_empty_content_inside(self):
        """
        Kiểm tra khi thẻ <startagent> có nhưng nội dung bên trong rỗng hoặc chỉ có khoảng trắng.
        Những khối rỗng này sẽ bị bỏ qua.
        """
        raw_text_content = "<startagent>  <endagent>Some valid text<startagent>[AgentB]: Content B<endagent><startagent>\n\t\r<endagent>"
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}]
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = ["[AgentB]: Content B"]
        actual_result = extract_content_from_response(agent_response)
        self.assertEqual(actual_result, expected_result)

    def test_extract_response_with_only_empty_tags(self):
        """
        Kiểm tra khi chỉ có các thẻ <startagent> rỗng.
        Hàm nên trả về một danh sách rỗng.
        """
        raw_text_content = "<startagent>  <endagent><startagent>\n\t\r<endagent>"
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}]
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = [] 
        actual_result = extract_content_from_response(agent_response)
        self.assertEqual(actual_result, expected_result)

    def test_extract_response_empty_raw_content(self):
        """
        Kiểm tra khi nội dung text của ConversationMessage là rỗng.
        """
        raw_text_content = ""
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}]
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = None
        actual_result = extract_content_from_response(agent_response)
        self.assertIsNone(actual_result)

    def test_extract_response_raw_content_is_none(self):
        """
        Kiểm tra khi text content bên trong ConversationMessage là None.
        """
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': None}] 
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = None 
        actual_result = extract_content_from_response(agent_response)
        self.assertIsNone(actual_result)

    def test_extract_response_conversation_message_content_list_empty(self):
        """
        Kiểm tra khi danh sách content của ConversationMessage rỗng.
        """
        output_message = ConversationMessage(role=ParticipantRole.ASSISTANT, content=[]) 
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = None
        actual_result = extract_content_from_response(agent_response)
        self.assertIsNone(actual_result)

    def test_extract_response_output_is_none(self):
        """
        Kiểm tra khi AgentResponse.output là None.
        """
        agent_response = AgentResponse(output=None, metadata=None, streaming=False)
        
        expected_result = None
        actual_result = extract_content_from_response(agent_response)
        self.assertIsNone(actual_result)

    def test_extract_response_agent_response_obj_is_none(self):
        """
        Kiểm tra khi chính đối tượng AgentResponse là None.
        """
        expected_result = None
        actual_result = extract_content_from_response(None)
        self.assertIsNone(actual_result)

    def test_extract_response_with_escaped_tags(self):
        """
        Kiểm tra các thẻ có thể có dấu backslash.
        The function extract_content_from_response expects '<endagent>' as the closing tag.
        """
        # CORRECTED: The second closing tag should be <endagent> to match the function's regex
        raw_text_content = "<\\startagent>[AgentC]: Escaped start tag<endagent> normal <startagent>[AgentD]: Normal tag<endagent>"
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}]
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_result = [
            "[AgentC]: Escaped start tag",
            "[AgentD]: Normal tag"
        ]
        actual_result = extract_content_from_response(agent_response)
        self.assertEqual(actual_result, expected_result)

    def test_extract_response_tags_with_newlines_and_spaces(self):
        """
        Kiểm tra thẻ và nội dung có nhiều dòng và khoảng trắng.
        """
        raw_text_content = (
            "  <startagent>  \n"
            "[MultiLineAgent]: This is a\n"
            "multi-line response.\n"
            "  It should be preserved.  \n"
            "<endagent>  "
        )
        output_message = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=[{'text': raw_text_content}]
        )
        agent_response = AgentResponse(output=output_message, metadata=None, streaming=False)
        
        expected_content = (
            "[MultiLineAgent]: This is a\n"
            "multi-line response.\n"
            "  It should be preserved."
        )
        expected_result = [expected_content.strip()]
        actual_result = extract_content_from_response(agent_response)
        
        self.assertIsNotNone(actual_result)
        self.assertEqual(len(actual_result), 1)
        self.assertEqual(actual_result[0].splitlines(), expected_content.strip().splitlines())

    def test_extract_response_output_is_string_no_tags(self):
        """
        Kiểm tra AgentResponse.output là một chuỗi string đơn giản, không có thẻ.
        """
        raw_text_content = "  This is a direct string output, no tags.  "
        
        output_message_with_string_content = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=raw_text_content 
        )
        agent_response_cm_string = AgentResponse(output=output_message_with_string_content, metadata=None, streaming=False)
        
        agent_response_direct_string = AgentResponse(output=raw_text_content, metadata=None, streaming=False)
        
        expected_result = ["This is a direct string output, no tags."]
        
        actual_result_cm_string = extract_content_from_response(agent_response_cm_string)
        self.assertEqual(actual_result_cm_string, expected_result)
        
        actual_result_direct_string = extract_content_from_response(agent_response_direct_string)
        self.assertEqual(actual_result_direct_string, expected_result)


    def test_extract_response_output_is_string_with_tags(self):
        """
        Kiểm tra AgentResponse.output là string và có thẻ.
        """
        raw_text_content = "Prefix <startagent>[AgentFromString]: Content from string<endagent> Suffix"
        
        output_message_with_string_content = ConversationMessage(
            role=ParticipantRole.ASSISTANT,
            content=raw_text_content
        )
        agent_response_cm_string = AgentResponse(output=output_message_with_string_content, metadata=None, streaming=False)

        agent_response_direct_string = AgentResponse(output=raw_text_content, metadata=None, streaming=False)
        
        expected_result = ["[AgentFromString]: Content from string"]

        actual_result_cm_string = extract_content_from_response(agent_response_cm_string)
        self.assertEqual(actual_result_cm_string, expected_result)

        actual_result_direct_string = extract_content_from_response(agent_response_direct_string)
        self.assertEqual(actual_result_direct_string, expected_result)

if __name__ == "__main__":
    unittest.main()
