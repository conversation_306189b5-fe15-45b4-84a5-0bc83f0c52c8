import requests
import pytest
import uuid
from datetime import datetime
from conftest import BASE_URL

# Test data
test_thread_name = f"Test Thread {uuid.uuid4()}"
test_thread_id = None  # Will be set after thread creation

def test_create_thread(auth_headers):
    """Test creating a new thread"""
    global test_thread_id

    # Prepare data for creating a thread
    data = {
        "name": test_thread_name,
        "tags": ["test", "api"],
        "metadata": {"source": "api_test"}
    }

    # Send POST request to create thread
    response = requests.post(f"{BASE_URL}/threads/", json=data, headers=auth_headers)

    # Assert response status code and content
    assert response.status_code == 201, f"Expected status code 201, got {response.status_code}"

    # Parse response JSON
    response_data = response.json()

    # Save thread ID for other tests
    test_thread_id = response_data["id"]

    # Assertions
    assert response_data["name"] == test_thread_name
    assert "test" in response_data["tags"]
    assert "api" in response_data["tags"]
    assert response_data["metadata"]["source"] == "api_test"

    print(f"Created thread with ID: {test_thread_id}")

def test_get_threads(auth_headers):
    """Test getting all threads for the user"""
    # Send GET request to get all threads
    response = requests.get(f"{BASE_URL}/threads/", headers=auth_headers)

    # Assert response status code
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # Parse response JSON
    threads = response.json()

    # Assert that threads is a list
    assert isinstance(threads, list)

    # Check if our test thread is in the list
    thread_found = False
    for thread in threads:
        if thread["id"] == test_thread_id:
            thread_found = True
            break

    assert thread_found, f"Test thread with ID {test_thread_id} not found in threads list"

    print(f"Found {len(threads)} threads")

def test_get_thread_by_id(auth_headers):
    """Test getting a specific thread by ID"""
    # Skip if test_thread_id is not set
    if not test_thread_id:
        pytest.skip("No thread ID available")

    # Send GET request to get the thread
    response = requests.get(f"{BASE_URL}/threads/{test_thread_id}", headers=auth_headers)

    # Assert response status code
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # Parse response JSON
    thread = response.json()

    # Assertions
    assert thread["id"] == test_thread_id
    assert thread["name"] == test_thread_name

    print(f"Retrieved thread: {thread['name']}")

def test_update_thread(auth_headers):
    """Test updating a thread"""
    # Skip if test_thread_id is not set
    if not test_thread_id:
        pytest.skip("No thread ID available")

    # New data for the thread
    updated_name = f"Updated Thread {uuid.uuid4()}"
    data = {
        "name": updated_name,
        "tags": ["updated", "test", "api"]
    }

    # Send PATCH request to update the thread
    response = requests.patch(f"{BASE_URL}/threads/{test_thread_id}", json=data, headers=auth_headers)

    # Assert response status code
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # Parse response JSON
    updated_thread = response.json()

    # Assertions
    assert updated_thread["id"] == test_thread_id
    assert updated_thread["name"] == updated_name
    assert "updated" in updated_thread["tags"]

    print(f"Updated thread name to: {updated_name}")

def test_delete_thread(auth_headers):
    """Test deleting a thread"""
    # Skip if test_thread_id is not set
    if not test_thread_id:
        pytest.skip("No thread ID available")

    # Send DELETE request to delete the thread
    response = requests.delete(f"{BASE_URL}/threads/{test_thread_id}", headers=auth_headers)

    # Assert response status code
    assert response.status_code == 204, f"Expected status code 204, got {response.status_code}"

    # Verify thread is deleted by trying to get it
    get_response = requests.get(f"{BASE_URL}/threads/{test_thread_id}", headers=auth_headers)
    assert get_response.status_code == 404, "Thread should not exist after deletion"

    print(f"Successfully deleted thread with ID: {test_thread_id}")

if __name__ == "__main__":
    # Get auth headers
    from conftest import get_bearer_token

    auth_headers = {
        "Authorization": f"Bearer {get_bearer_token()}",
        "Content-Type": "application/json"
    }

    # Run tests in order
    test_create_thread(auth_headers)
    test_get_threads(auth_headers)
    test_get_thread_by_id(auth_headers)
    test_update_thread(auth_headers)
    test_delete_thread(auth_headers)
    print("All thread endpoint tests passed!")
