import requests
import pytest
from conftest import BASE_URL, get_bearer_token

def test_get_current_user(auth_headers):
    """Test getting information about the current user"""
    # Send GET request to get current user info
    response = requests.get(f"{BASE_URL}/users/me", headers=auth_headers)

    # Assert response status code
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # Parse response JSON
    user = response.json()

    # Assertions
    assert "id" in user
    assert "identifier" in user
    assert "createdAt" in user

    print(f"Retrieved current user with ID: {user['id']}")
    print(f"User identifier: {user['identifier']}")

def test_get_current_user_without_token():
    """Test getting current user without authentication token"""
    # Headers without authentication
    headers_without_auth = {
        "Content-Type": "application/json"
    }

    # Send GET request without auth token
    response = requests.get(f"{BASE_URL}/users/me", headers=headers_without_auth)

    # Assert response status code (should be 401 Unauthorized)
    assert response.status_code == 401, f"Expected status code 401, got {response.status_code}"

    print("Successfully detected unauthorized access attempt")

def test_get_current_user_with_invalid_token():
    """Test getting current user with invalid authentication token"""
    # Headers with invalid authentication
    headers_with_invalid_auth = {
        "Authorization": "Bearer invalid_token",
        "Content-Type": "application/json"
    }

    # Send GET request with invalid auth token
    response = requests.get(f"{BASE_URL}/users/me", headers=headers_with_invalid_auth)

    # Assert response status code (should be 401 Unauthorized)
    assert response.status_code == 401, f"Expected status code 401, got {response.status_code}"

    print("Successfully detected invalid token")

def test_get_current_user_with_duplicate_bearer_prefix():
    """Test getting current user with duplicate Bearer prefix in token"""
    # Get valid token
    token = get_bearer_token()
    if not token:
        pytest.skip("Could not obtain authentication token")

    # Headers with duplicate Bearer prefix
    headers_with_duplicate_bearer = {
        "Authorization": f"Bearer Bearer {token}",
        "Content-Type": "application/json"
    }

    # Send GET request with duplicate Bearer prefix
    response = requests.get(f"{BASE_URL}/users/me", headers=headers_with_duplicate_bearer)

    # Assert response status code (should be 200 OK because our implementation handles duplicate Bearer prefix)
    assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"

    # Parse response JSON
    user = response.json()

    # Assertions
    assert "id" in user
    assert "identifier" in user

    print("Successfully handled duplicate Bearer prefix")

if __name__ == "__main__":
    # Get auth headers
    auth_headers = {
        "Authorization": f"Bearer {get_bearer_token()}",
        "Content-Type": "application/json"
    }

    # Run tests
    test_get_current_user(auth_headers)
    test_get_current_user_without_token()
    test_get_current_user_with_invalid_token()
    test_get_current_user_with_duplicate_bearer_prefix()

    print("All user endpoint tests passed!")
