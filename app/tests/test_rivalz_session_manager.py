"""
Test script for the Rivalz session manager.

This script tests the functionality of the Rivalz session manager,
including session creation, retrieval, and cleanup.
"""

import asyncio
import time # Keep time for potential non-async parts if any, though asyncio.sleep is preferred for async tests
import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.services.rivalz_session_manager import RivalzSessionManager
from app.services.rivalz_agent import RivalzAgent
# from rAgent.storage import InMemoryChatStorage # Not directly used in these tests

class TestRivalzSessionManager(unittest.TestCase):
    """Test cases for the Rivalz session manager."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create a session manager with shorter timeouts for testing
        # Due to the singleton nature of RivalzSessionManager, these parameters
        # might only take effect if this is the first instantiation in the test suite.
        # For robust testing, consider making RivalzSessionManager non-singleton for tests
        # or providing a reset/reconfigure method.
        self.session_manager = RivalzSessionManager(cleanup_interval=1, session_timeout=2)
        
        # Mock the RivalzAgent.create method
        self.patcher = patch('app.services.rivalz_agent.RivalzAgent.create')
        self.mock_create = self.patcher.start()
        
        # Create a mock agent
        self.mock_agent = MagicMock(spec=RivalzAgent)
        self.mock_create.return_value = self.mock_agent
        
        # Get the current event loop for running async code
        self.loop = asyncio.get_event_loop()
        # If tests are run in parallel or with specific asyncio policies, 
        # you might need asyncio.new_event_loop() and asyncio.set_event_loop(self.loop) here,
        # and self.loop.close() in tearDown. For standard unittest, get_event_loop() is often sufficient.
    
    def tearDown(self):
        """Tear down test fixtures."""
        self.patcher.stop()
        # Attempt to cancel the background cleanup task of the session manager
        if hasattr(self.session_manager, '_cleanup_task') and self.session_manager._cleanup_task:
            cleanup_task = self.session_manager._cleanup_task
            if not cleanup_task.done(): # Check if the task is not already done
                cleanup_task.cancel()
                try:
                    # Give the event loop a chance to process the cancellation
                    self.loop.run_until_complete(asyncio.sleep(0)) 
                    # Optionally, await the task to ensure it's handled, but cancellation might raise an error here
                    # self.loop.run_until_complete(cleanup_task) 
                except asyncio.CancelledError:
                    pass # Expected
                except RuntimeError as e: 
                    # This can happen if the loop is already closed or being closed
                    print(f"RuntimeError during cleanup task cancellation in tearDown: {e}")
        # If you used new_event_loop() in setUp:
        # self.loop.close()
    
    def test_get_or_create_agent(self):
        """Test getting or creating an agent."""
        self.loop.run_until_complete(self._test_get_or_create_agent())
    
    async def _test_get_or_create_agent(self):
        """Async implementation of test_get_or_create_agent."""
        project_id = "test_project_1"
        agent1 = await self.session_manager.get_or_create_agent(project_id)
        
        self.mock_create.assert_called_once()
        self.assertEqual(agent1, self.mock_agent)
        
        self.mock_create.reset_mock()
        
        agent2 = await self.session_manager.get_or_create_agent(project_id)
        
        self.mock_create.assert_not_called()
        self.assertEqual(agent2, self.mock_agent)
        
        project_id2 = "test_project_2"
        await self.session_manager.get_or_create_agent(project_id2)
        
        self.mock_create.assert_called_once() # Called for project_id2
    
    def test_cleanup_inactive_sessions(self):
        """Test cleaning up inactive sessions."""
        self.loop.run_until_complete(self._test_cleanup_inactive_sessions())
    
    async def _test_cleanup_inactive_sessions(self):
        """Async implementation of test_cleanup_inactive_sessions."""
        project_id1 = "test_project_cleanup_1"
        project_id2 = "test_project_cleanup_2"
        
        # Create agents. Their last_active_time is roughly T0.
        await self.session_manager.get_or_create_agent(project_id1)
        await self.session_manager.get_or_create_agent(project_id2)
        
        self.assertEqual(self.session_manager.get_active_sessions_count(), 2)
        
        # Let some time pass, but less than session_timeout.
        # cleanup_interval = 1s, session_timeout = 2s.
        # A cleanup might run at T0+1s. Both agents are 1s old, so they are safe (< 2s timeout).
        await asyncio.sleep(1.5) # Current time is roughly T0 + 1.5s
        
        # Update project_id1. Its last_active_time is now T0 + 1.5s.
        # project_id2's last_active_time is still T0.
        self.session_manager.update_last_active(project_id1)
        
        # Wait for another period.
        # Total time elapsed for project_id2 since creation: 1.5s + 1.5s = 3s.
        # Time elapsed for project_id1 since its last update: 1.5s.
        # Cleanup at T0+2s (approx):
        #   - P1: age = (T0+2s) - (T0+1.5s) = 0.5s. Safe (< 2s).
        #   - P2: age = (T0+2s) - T0 = 2s. Safe (condition is age > timeout, so 2 > 2 is false).
        # Cleanup at T0+3s (approx):
        #   - P1: age = (T0+3s) - (T0+1.5s) = 1.5s. Safe (< 2s).
        #   - P2: age = (T0+3s) - T0 = 3s. Cleaned (3s > 2s is true).
        await asyncio.sleep(1.5) # Current time is roughly T0 + 3s

        # Verify that only project 1 is still in the session manager
        self.assertEqual(self.session_manager.get_active_sessions_count(), 1)
        self.assertIn(project_id1, self.session_manager.agents)
        self.assertNotIn(project_id2, self.session_manager.agents)

if __name__ == "__main__":
    unittest.main()
