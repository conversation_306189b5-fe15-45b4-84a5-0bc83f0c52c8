# RAgent Backend API

## Overview
This is the backend API service for RAgent, a multi-agent orchestration system built with FastAPI and Python. It provides endpoints for managing agent conversations, thread management, and user interactions.

## Features
- Async API support using SQLAlchemy 2.0 and asyncpg
- Multi-agent orchestration system
- Thread-based conversation management
- Production-ready configuration with Docker
- Built-in monitoring and health checks

## Prerequisites
- Python 3.11+
- PostgreSQL 15+
- Redis (for caching and rate limiting)
- Docker and Docker Compose (for production deployment)

## Installation

### Local Development Setup
1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/MacOS
venv\Scripts\activate     # Windows
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment variables:
Create a `.env` file with:
```
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
OPENAI_API_KEY=your_openai_api_key
ENVIRONMENT=development
```

### Production Deployment
See `PRODUCTION.md` for detailed deployment instructions.

## API Documentation
- Swagger UI: `/docs`
- ReDoc: `/redoc`
- Async API Guide: See `ASYNC_API.md`

## Testing
Run tests using:
```bash
pytest tests/
```

## Contributing
1. Fork the repository
2. Create a feature branch
3. Submit a pull request

## License
Proprietary - All rights reserved

