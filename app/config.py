# Configuration settings for the application
import os
from typing import Optional
from pydantic import BaseModel

# Load .env 
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  

class Settings(BaseModel):
    """
    settings with .env file.
    """
    DATABASE_URL: str = os.getenv("DATABASE_URL", "")

    # API Key cho OpenAI
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    
    CHATGPT_MODEL: str = "gpt-4o"  # Hoặc gpt-4, ...

    MAX_HISTORY_MESSAGES: int = 20

    SKIP_AUTH_FOR_TESTS: bool = os.getenv("SKIP_AUTH_FOR_TESTS", "False").lower() == "true"

    # Rivalz service settings
    RIVAL_URL: str = os.getenv("RIVAL_URL", "https://staging-rome-api-v2.rivalz.ai")
    RIVAL_AUTH_KEY: Optional[str] = os.getenv("RIVAL_AUTH_KEY", "")
    USE_RIVALZ_SERVICE: bool = os.getenv("USE_RIVALZ_SERVICE", "False").lower() in ("true", "1", "t")
    SESSION_TIMEOUT_MINUTES: int = int(os.getenv("SESSION_TIMEOUT_MINUTES", "60"))
    RIVALZ_PROJECT_ID: str = os.getenv("RIVALZ_PROJECT_ID", "default_project_id")
    DEEP_INFRA_API_KEY: str = os.getenv("deep_infra_api_key", "")
    DEEP_INFRA_URL: str = os.getenv("base_url", "")
    DEEP_INFRA_MODEL: str = os.getenv("deep_infra_model", "")

settings = Settings()
