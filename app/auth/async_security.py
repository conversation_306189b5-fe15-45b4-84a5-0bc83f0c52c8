# /app/core/async_security.py
import httpx # Library to call external API
from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
import logging
import time

from ..db import async_crud, models
from ..db.async_database import get_async_db_dependency # Import async get_db dependency
from .. import dtos # Import schemas to create UserCreate
from ..config import settings # Import configuration

logger = logging.getLogger(__name__)

async def get_external_user_info(project_id: str) -> dict:
    """
    Call external authentication API (Rival) to get project information.

    Args:
        project_id: Project ID received from client header.

    Returns:
        Dictionary containing project information from external API, or raise HTTPException if error.
    """
    if not project_id:
        logger.warning("Empty project_id provided.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication. Project ID cannot be empty.",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Get authentication key from settings
    auth_key = settings.RIVAL_AUTH_KEY
    if not auth_key:
        logger.error("RIVAL_AUTH_KEY not configured in environment variables")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service misconfigured"
        )

    # Construct the external API URL
    external_api_url = f"{settings.RIVAL_URL}/agent/swarm?authen_key={auth_key}&project_id={project_id}"
    logger.info(f"RIVAL_URL from settings: {settings.RIVAL_URL}")
    logger.info(f"Full URL to call authentication API: {external_api_url}")

    # Make the API call
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            logger.info(f"Calling Rival authentication API: {external_api_url}")
            start_time = time.time()
            response = await client.get(external_api_url)
            request_time = time.time() - start_time

            logger.info(f"Rival authentication API returned status code: {response.status_code} in {request_time:.2f}s")
            logger.info(f"Response headers: {dict(response.headers)}")

            # Check for successful response
            response.raise_for_status() # Raise error if status code is 4xx or 5xx

            # Parse and validate response data
            response_data = response.json()
            logger.info(f"Received data from Rival API: {response_data}")
            
            # Check if response has valid format with code=0 and resources
            if response_data.get('code') != 0 or 'data' not in response_data:
                logger.error(f"Invalid response format from Rival API: {response_data}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Project authentication failed",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Extract project info and resources
            project_info = response_data.get('data', {}).get('info', {})
            resources = response_data.get('data', {}).get('resources', {})
            
            if not project_info or not resources:
                logger.error(f"Missing project info or resources in response: {response_data}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid project information received",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Return formatted project data
            return {
                "project_name": project_info.get('name'),
                "project_id": project_info.get('_id'),
                "resources": resources
            }

        except httpx.HTTPStatusError as e:
            # Handle HTTP errors (4xx, 5xx)
            logger.error(f"Error calling Rival authentication API: {e.response.status_code} - {e.response.text}", exc_info=True)
            logger.error(f"API URL: {external_api_url}")
            
            # Special handling for 401 errors
            if e.response.status_code == 401:
                error_detail = "Invalid project ID"
                try:
                    # Try to extract more detailed error message from response
                    error_json = e.response.json()
                    if 'detail' in error_json:
                        error_detail = f"Rival auth error: {error_json['detail']}"
                    elif 'message' in error_json:
                        error_detail = f"Rival auth error: {error_json['message']}"
                    logger.error(f"Auth API 401 response body: {error_json}")
                except Exception as json_err:
                    logger.error(f"Could not parse 401 response as JSON: {str(json_err)}")
                    logger.error(f"Raw 401 response body: {e.response.text}")

                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=error_detail,
                    headers={"WWW-Authenticate": "Bearer"},
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Rival authentication service error with status {e.response.status_code}"
                )
        except httpx.RequestError as e:
            # Handle connection errors
            logger.error(f"Connection error to Rival authentication API: {e}", exc_info=True)
            logger.error(f"API URL: {external_api_url}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"Unable to connect to Rival authentication service: {str(e)}"
            )
        except Exception as e:
            # Handle unexpected errors
            logger.error(f"Unexpected error while calling Rival authentication API: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred during authentication: {str(e)}"
            )

async def get_current_user_async(
    request: Request,
    db: AsyncSession = Depends(get_async_db_dependency)
) -> models.User:
    """
    Async dependency to get current user information:
    1. Get project_id from request header.
    2. Call Rival authentication API.
    3. Get or create corresponding user in local DB using async functions.
    """
    # Check if in skip authentication mode for tests
    if settings.SKIP_AUTH_FOR_TESTS:
        logger.warning("Skipping authentication because SKIP_AUTH_FOR_TESTS=true")
        # Create or get a fixed test user
        test_user = await async_crud.get_user_by_identifier(db, identifier="test_user")
        if not test_user:
            user_in = dtos.UserCreate(
                identifier="test_user",
                metadata={"is_test_user": True, "name": "Test User"}
            )
            test_user = await async_crud.create_user(db=db, user=user_in)
            logger.info(f"Created test user with ID: {test_user.id}")
        return test_user

    # Get project_id from header
    project_id = request.headers.get("project-id")
    
    # Check if project_id is provided
    if not project_id:
        logger.warning("Missing project-id header")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing project-id header",
            headers={"WWW-Authenticate": "Bearer"},
        )

    logger.info(f"Authenticating with project_id: {project_id}")

    # Call Rival API with the project_id
    project_data = await get_external_user_info(project_id)

    # Extract project identifier
    project_identifier = project_data.get("project_id")
    if not project_identifier:
        logger.error(f"Could not find project_id in response from Rival API: {project_data}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not extract project identifier from authentication service response"
        )
    
    # Ensure it's a string
    project_identifier = str(project_identifier)

    # Get or create user in local DB
    db_user = await async_crud.get_user_by_identifier(db, identifier=project_identifier)
    if db_user is None:
        logger.info(f"Project with identifier '{project_identifier}' not found in local DB. Creating new...")
        user_metadata = {
            "project_name": project_data.get("project_name"),
            "resources": project_data.get("resources")
        }
        user_in = dtos.UserCreate(
            identifier=project_identifier, 
            name=project_data.get("project_name"),
            metadata=user_metadata
        )
        try:
            db_user = await async_crud.create_user(db=db, user=user_in)
            logger.info(f"Successfully created local user with ID: {db_user.id}")
        except Exception as e:
            logger.error(f"Error creating local user: {e}", exc_info=True)
            await db.rollback()
            db_user = await async_crud.get_user_by_identifier(db, identifier=project_identifier) # Try to get again
            if not db_user:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Could not create local user record."
                )
    else:
        # Update user metadata with latest project data
        logger.info(f"Found local user with ID: {db_user.id} for project: {project_identifier}")
        try:
            updated_metadata = {
                "project_name": project_data.get("project_name"),
                "resources": project_data.get("resources")
            }
            # Sử dụng setattr để gán metadata_
            setattr(db_user, 'metadata_', updated_metadata)
            # Đảm bảo name không None
            project_name = project_data.get("project_name")
            if project_name is not None:
                setattr(db_user, 'name', str(project_name))
            # updatedAt will be set automatically by the onupdate trigger
            await db.commit()
            await db.refresh(db_user)
            logger.info(f"Updated metadata and name for user with ID: {db_user.id}")
        except Exception as e:
            logger.error(f"Error updating user metadata: {e}", exc_info=True)
            await db.rollback()

    return db_user
