# ===== PRODUCTION DEPENDENCIES =====
fastapi==0.104.0
gunicorn==21.2.0
httpx==0.25.0
openai==1.67.0
psycopg2-binary==2.9.9
pydantic==2.10.1
pydantic-settings==2.0.0
python-dotenv==1.0.1
sqlalchemy[asyncio]>=2.0.0
sqlalchemy>=2.0.0
uvicorn[standard]==0.23.0

# ===== OPTIONAL DEPENDENCIES =====
aiohttp==3.11.12
anthropic
boto3
botocore
fastapi-limiter==0.1.5
prometheus-fastapi-instrumentator==6.1.0
python-json-logger==2.0.7
redis>=4.2.0
starlette-context==0.3.6

# ===== DATABASE (OPTIONAL/TEST) =====
aiosqlite==0.19.0
asyncpg==0.28.0
libsql-client

# ===== TESTING DEPENDENCIES =====
coverage
moto
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-mock

# ===== YAML SUPPORT =====
PyYAML