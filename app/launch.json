{"version": "0.2.0", "configurations": [{"name": "FastAPI Debug", "type": "python", "request": "launch", "program": "${workspaceFolder}/app/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": []}, {"name": "FastAPI Uvicorn Debug", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}]}