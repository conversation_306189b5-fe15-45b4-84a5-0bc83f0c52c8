# API Request/Response DTOs (Data Transfer Objects)
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# --- User DTOs ---
class UserBase(BaseModel):
    # Identifier từ hệ thống bên ngoài là bắt buộc khi tạo/đọc user
    identifier: str
    # Tên hiển thị của user/project
    name: Optional[str] = None
    # Sử dụng alias để field trong Python là metadata_ nhưng JSON là metadata
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

class UserCreate(UserBase):
    pass # Không cần thêm trường nào khi tạo

class UserResponse(UserBase):
    id: str # ID nội bộ của user trong hệ thống chúng ta
    createdAt: datetime
    updatedAt: Optional[datetime] = None

    model_config = {
        "from_attributes": True,  # Cho phép Pydantic đọc dữ liệu từ ORM models (thay thế cho orm_mode)
        "populate_by_name": True  # Cho phép dùng alias 'metadata' khi tạo instance (thay thế cho allow_population_by_field_name)
    }

    # Đảm bảo metadata luôn là dict
    @classmethod
    def model_validate(cls, obj, *args, **kwargs):
        if hasattr(obj, 'metadata_') and obj.metadata_ is None:
            obj.metadata_ = {}
        return super().model_validate(obj, *args, **kwargs)


# --- Thread DTOs ---
class ThreadBase(BaseModel):
    name: Optional[str] = None
    tags: Optional[List[str]] = Field(default_factory=list)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

class ThreadCreate(ThreadBase):
    pass

class ThreadUpdate(BaseModel):
    """DTO để cập nhật Thread (chỉ cho phép cập nhật một số trường)."""
    name: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class ThreadResponse(ThreadBase):
    id: str
    createdAt: datetime
    userId: str # ID của user sở hữu thread (từ DB nội bộ)
    userIdentifier: Optional[str] # Identifier của user (từ hệ thống ngoài)

    model_config = {
        "from_attributes": True,
        "populate_by_name": True
    }

    # Đảm bảo metadata luôn là dict
    @classmethod
    def model_validate(cls, obj, *args, **kwargs):
        if hasattr(obj, 'metadata_') and obj.metadata_ is None:
            obj.metadata_ = {}
        return super().model_validate(obj, *args, **kwargs)


# --- Step DTOs ---
class StepBase(BaseModel):
    # Các trường cơ bản cần thiết khi tạo Step
    name: str
    type: str # Ví dụ: 'USER_MESSAGE', 'AI_RESPONSE', 'TOOL_CALL', 'TOOL_RESULT', 'ERROR'
    input: Optional[str] = None
    output: Optional[str] = None
    parentId: Optional[str] = None # ID của step cha (nếu có)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    tags: Optional[List[str]] = Field(default_factory=list)
    language: Optional[str] = None
    streaming: bool = False # Mặc định là không streaming khi tạo step

class StepCreate(StepBase):
    # Các trường cần thiết khi tạo Step, ví dụ:
    name: str = Field(..., description="Tên của step.")
    type: str = Field(..., description="Loại step (USER_MESSAGE, AI_RESPONSE, ERROR, etc.).")
    # input và output có thể là bắt buộc hoặc không tùy logic
    pass

class StepResponse(StepBase):
    # Các trường trả về cho client
    id: str
    threadId: str
    createdAt: datetime
    start: Optional[datetime] = None
    end: Optional[datetime] = None
    isError: bool
    # Thêm trường feedback
    like: bool = False
    dislike: bool = False

    model_config = {
        "from_attributes": True,
        "populate_by_name": True
    }

# --- Chat DTOs ---
class ChatRequest(BaseModel):
    thread_id: str = Field(..., description="ID của cuộc trò chuyện (thread) hiện tại.")
    message: str = Field(..., description="Nội dung tin nhắn từ người dùng.")
    agent_type: Optional[str] = Field(default="rx", description="Loại agent được chọn để xử lý yêu cầu (ví dụ: 'rx', 're', 'rc').")

class ChatResponse(BaseModel):
    thread_id: str
    ai_response: List[str] = Field(description="Danh sách nội dung phản hồi từ AI (từ các agents khác nhau).")
    user_step: StepResponse = Field(description="Thông tin chi tiết về Step của tin nhắn người dùng vừa được lưu.")
    ai_steps: List[StepResponse] = Field(description="Danh sách các Step chi tiết của (các) phản hồi AI vừa được lưu.")


# --- Feedback DTOs ---
class FeedbackCreate(BaseModel):
    forId: str = Field(..., description="ID của Step mà feedback này dành cho.")
    value: int = Field(..., description="Giá trị feedback (ví dụ: 1 = thumbs up, -1 = thumbs down).")
    comment: Optional[str] = None

class FeedbackResponse(FeedbackCreate):
    id: str
    threadId: str
    createdAt: datetime

    model_config = {
        "from_attributes": True
    }

# --- Element DTOs (Ví dụ nếu cần API riêng cho Element) ---
class ElementBase(BaseModel):
    name: str
    type: Optional[str] = None
    url: Optional[str] = None
    display: Optional[str] = 'inline' # Mặc định hiển thị inline
    forId: Optional[str] = None # ID của Step liên quan (nếu có)
    mime: Optional[str] = None

class ElementCreate(ElementBase):
    threadId: str # Cần biết thread nào khi tạo element

class ElementResponse(ElementBase):
    id: str
    threadId: str

    model_config = {
        "from_attributes": True
    }
