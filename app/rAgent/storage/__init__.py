"""
Storage implementations for chat history.
"""
from .chat_storage import Cha<PERSON><PERSON><PERSON><PERSON>
from .in_memory_chat_storage import InMemoryChatStorage

_AWS_AVAILABLE = False
_SQL_AVAILABLE = False

try:
    from .dynamodb_chat_storage import DynamoDb<PERSON>hatStorage
    _AWS_AVAILABLE = True
except ImportError:
    _AWS_AVAILABLE = False

try:
    from .sql_chat_storage import SqlChatStorage
    _SQL_AVAILABLE = True
except ImportError:
    _SQL_AVAILABLE = False

__all__ = [
    'ChatStorage',
    'InMemoryChatStorage',
]

if _AWS_AVAILABLE:
    __all__.extend([
        'DynamoDbChatStorage'
    ])

if _SQL_AVAILABLE:
    __all__.extend([
        'SqlChatStorage'
    ])