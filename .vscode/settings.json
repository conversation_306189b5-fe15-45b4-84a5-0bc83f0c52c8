{"python.defaultInterpreterPath": "/opt/miniconda3/envs/runx/bin/python", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.env": false}, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python-envs.defaultEnvManager": "ms-python.python:conda", "python-envs.defaultPackageManager": "ms-python.python:conda", "python-envs.pythonProjects": []}