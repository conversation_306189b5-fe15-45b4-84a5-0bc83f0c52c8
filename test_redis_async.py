#!/usr/bin/env python3
"""Test Redis async functionality"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, '.')

async def test_redis_async():
    """Test Redis async functionality"""
    print("🔄 Testing Redis async functionality...")
    
    try:
        # Test Redis library import
        print("🔄 Testing Redis import...")
        try:
            import redis.asyncio as redis_async
            print("✅ redis.asyncio imported successfully")
            
            # Test Redis URL creation
            REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
            REDIS_PORT = os.getenv("REDIS_PORT", "6379")
            REDIS_DB = os.getenv("REDIS_DB", "0")
            REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")
            
            if REDIS_PASSWORD:
                REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
            else:
                REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
            
            print(f"✅ Redis URL: {REDIS_URL}")
            
            # Test Redis client creation
            print("🔄 Creating Redis async client...")
            client = redis_async.from_url(
                REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=2.0,
                socket_connect_timeout=2.0,
                retry_on_timeout=True
            )
            print("✅ Redis async client created")
            
            # Test Redis operations with timeout
            print("🔄 Testing Redis operations...")
            try:
                # Test ping
                await asyncio.wait_for(client.ping(), timeout=3.0)
                print("✅ Redis ping successful")
                
                # Test set/get
                await asyncio.wait_for(client.set("test_async", "async_value", ex=60), timeout=3.0)
                print("✅ Redis set successful")
                
                value = await asyncio.wait_for(client.get("test_async"), timeout=3.0)
                print(f"✅ Redis get successful: {value}")
                
                # Cleanup
                await asyncio.wait_for(client.delete("test_async"), timeout=3.0)
                print("✅ Redis delete successful")
                
                # Close connection
                await client.aclose()
                print("✅ Redis connection closed")
                
            except asyncio.TimeoutError:
                print("⚠️ Redis operations timed out - Redis server may not be running")
            except Exception as e:
                print(f"⚠️ Redis operations failed: {e}")
                
        except ImportError as e:
            print(f"❌ Redis async import failed: {e}")
            print("💡 Try: pip install redis[hiredis]")
            
        # Test our cache module
        print("\n🔄 Testing our cache module...")
        try:
            from app.middleware.cache import REDIS_ASYNC_AVAILABLE, _safe_redis_call
            print(f"✅ Cache module imported, REDIS_ASYNC_AVAILABLE: {REDIS_ASYNC_AVAILABLE}")
            
            # Test safe redis call with a simple operation
            if REDIS_ASYNC_AVAILABLE:
                print("✅ Redis async is available in cache module")
            else:
                print("⚠️ Redis async not available, using sync fallback")
                
        except Exception as e:
            print(f"❌ Cache module test failed: {e}")
            
        print("\n✅ Redis async test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_redis_async())
