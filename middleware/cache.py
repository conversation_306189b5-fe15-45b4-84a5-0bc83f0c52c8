# /app/core/cache.py
import json
import logging
from typing import Any, Optional, Union, Dict
import os
import datetime

from .config import settings

# Thử import redis, bỏ qua nếu không có
try:
    import redis.asyncio as redis
    from fastapi import FastAPI
    from fastapi_limiter import FastAPILimiter
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

# Cấu hình Redis URL từ biến môi trường hoặc mặc định
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
REDIS_HOST = os.getenv("REDIS_HOST", "localhost" if ENVIRONMENT == "development" else "redis")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")
REDIS_DB = os.getenv("REDIS_DB", "0")
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")

# Tạo Redis URL
if REDIS_PASSWORD:
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
else:
    REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# Tạo một JSON encoder tùy chỉnh để xử lý các đối tượng datetime
class DateTimeEncoder(json.JSONEncoder):
    """JSON encoder tùy chỉnh để xử lý các đối tượng datetime."""
    def default(self, obj):
        if isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
            return obj.isoformat()
        return super().default(obj)

# Khởi tạo Redis client
redis_client = None

async def init_redis_client():
    """
    Khởi tạo và trả về Redis client.
    """
    global redis_client

    if not REDIS_AVAILABLE:
        logger.warning("Redis is not available. Cache and rate limiting will be disabled.")
        return None

    if redis_client is None:
        try:
            # Thêm các tùy chọn kết nối để cải thiện độ tin cậy
            redis_client = redis.from_url(
                REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=5.0,       # Timeout cho socket operations
                socket_connect_timeout=5.0,  # Timeout cho kết nối
                retry_on_timeout=True,    # Tự động thử lại khi timeout
                health_check_interval=30  # Kiểm tra kết nối mỗi 30 giây
            )
            # Kiểm tra quyền ghi
            await redis_client.set("connection_test", "1", ex=10)
            await redis_client.delete("connection_test")
            logger.info(f"Redis client initialized with URL: {REDIS_URL} (write access confirmed)")
        except redis.exceptions.ReadOnlyError as e:
            logger.error(f"Redis is in read-only mode: {e}. Cache writes will be disabled.", exc_info=True)
            # Vẫn giữ client để đọc cache, nhưng ghi sẽ bị vô hiệu hóa
        except Exception as e:
            logger.error(f"Failed to initialize Redis client: {e}", exc_info=True)
            redis_client = None
    return redis_client

async def setup_redis(app) -> None:
    """
    Thiết lập Redis và các tính năng phụ thuộc như rate limiting.
    """
    if not REDIS_AVAILABLE:
        logger.warning("Redis library not installed. Cache and rate limiting will be disabled.")
        return

    # Kiểm tra xem Redis có sẵn không
    try:
        redis_instance = await init_redis_client()
        if not redis_instance:
            logger.warning("Could not connect to Redis. Cache and rate limiting will be disabled.")
            return

        # Ping để kiểm tra kết nối
        await redis_instance.ping()
        logger.info(f"Successfully connected to Redis at {REDIS_URL}")

        # Thiết lập rate limiting
        try:
            await FastAPILimiter.init(redis_instance)
            logger.info("FastAPI Limiter initialized with Redis")
        except Exception as e:
            logger.error(f"Failed to initialize FastAPI Limiter: {e}")
    except Exception as e:
        logger.warning(f"Redis connection failed: {e}. Cache and rate limiting will be disabled.")

async def get_cache(key: str) -> Optional[Any]:
    """
    Lấy giá trị từ cache.
    """
    if not REDIS_AVAILABLE or not redis_client:
        return None

    try:
        data = await redis_client.get(key)
        if data:
            # Parse JSON data
            parsed_data = json.loads(data)

            # Xử lý các chuỗi ISO datetime
            if isinstance(parsed_data, dict):
                for k, v in parsed_data.items():
                    if isinstance(v, str) and len(v) > 10:
                        try:
                            # Thử chuyển đổi chuỗi ISO thành datetime
                            parsed_data[k] = datetime.datetime.fromisoformat(v)
                        except ValueError:
                            # Nếu không phải chuỗi ISO datetime, giữ nguyên giá trị
                            pass
            elif isinstance(parsed_data, list):
                for i, item in enumerate(parsed_data):
                    if isinstance(item, dict):
                        for k, v in item.items():
                            if isinstance(v, str) and len(v) > 10:
                                try:
                                    # Thử chuyển đổi chuỗi ISO thành datetime
                                    item[k] = datetime.datetime.fromisoformat(v)
                                except ValueError:
                                    # Nếu không phải chuỗi ISO datetime, giữ nguyên giá trị
                                    pass

            return parsed_data
        return None
    except Exception as e:
        logger.error(f"Error getting cache for key {key}: {e}", exc_info=True)
        return None

async def set_cache(key: str, value: Any, expire: int = 3600) -> bool:
    """
    Lưu giá trị vào cache.

    Args:
        key: Khóa cache
        value: Giá trị cần lưu (sẽ được chuyển thành JSON)
        expire: Thời gian hết hạn (giây), mặc định là 1 giờ

    Returns:
        bool: True nếu thành công, False nếu thất bại
    """
    if not REDIS_AVAILABLE or not redis_client:
        return False

    try:
        # Sử dụng DateTimeEncoder để xử lý các đối tượng datetime
        serialized_value = json.dumps(value, cls=DateTimeEncoder)
        await redis_client.set(key, serialized_value, ex=expire)
        return True
    except redis.exceptions.ReadOnlyError as e:
        # Xử lý trường hợp Redis ở chế độ read-only
        logger.warning(f"Redis is in read-only mode, cannot write to cache: {e}")
        # Không ghi log lỗi đầy đủ để tránh làm đầy log file
        return False
    except Exception as e:
        logger.error(f"Error setting cache for key {key}: {e}", exc_info=True)
        return False

async def delete_cache(key: str) -> bool:
    """
    Xóa một khóa khỏi cache.
    """
    if not REDIS_AVAILABLE or not redis_client:
        return False

    try:
        await redis_client.delete(key)
        return True
    except redis.exceptions.ReadOnlyError as e:
        # Xử lý trường hợp Redis ở chế độ read-only
        logger.warning(f"Redis is in read-only mode, cannot delete from cache: {e}")
        return False
    except Exception as e:
        logger.error(f"Error deleting cache for key {key}: {e}", exc_info=True)
        return False

async def clear_cache_pattern(pattern: str) -> bool:
    """
    Xóa tất cả các khóa khớp với pattern.
    """
    if not REDIS_AVAILABLE or not redis_client:
        return False

    try:
        cursor = 0
        while True:
            cursor, keys = await redis_client.scan(cursor, match=pattern, count=100)
            if keys:
                await redis_client.delete(*keys)
            if cursor == 0:
                break
        return True
    except redis.exceptions.ReadOnlyError as e:
        # Xử lý trường hợp Redis ở chế độ read-only
        logger.warning(f"Redis is in read-only mode, cannot clear cache with pattern {pattern}: {e}")
        return False
    except Exception as e:
        logger.error(f"Error clearing cache with pattern {pattern}: {e}", exc_info=True)
        return False
