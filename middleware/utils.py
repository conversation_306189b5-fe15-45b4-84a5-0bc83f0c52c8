import datetime
import collections.abc
from typing import Any

def serialize_complex_objects(data: Any) -> Any:
    """Chuyển đổi các object phức tạp thành dạng có thể serialize JSON"""
    if isinstance(data, collections.abc.Mapping):
        return {
            k: serialize_complex_objects(v) for k, v in data.items()
        }
    elif isinstance(data, collections.abc.Sequence) and not isinstance(data, (str, bytes)):
        return [serialize_complex_objects(item) for item in data]
    elif isinstance(data, datetime.datetime):
        return data.isoformat()
    elif hasattr(data, 'role') and hasattr(data, 'content'):
        # Đ<PERSON>y có thể là ConversationMessage
        return {
            "role": str(data.role.value) if hasattr(data.role, 'value') else str(data.role),
            "content": serialize_complex_objects(data.content) if hasattr(data, 'content') else None,
            "type": "ConversationMessage"
        }
    elif hasattr(data, '__dict__'):
        # Với các object khác có __dict__, chuyển thành dict
        return serialize_complex_objects({k: v for k, v in data.__dict__.items() if not k.startswith('_')})
    else:
        return data