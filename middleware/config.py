# Configuration settings for the application
import os
from typing import Optional, Dict, Any
from pydantic import Field, AnyHttpUrl, BaseModel

# Thử import pydantic_settings nếu có
try:
    from pydantic_settings import BaseSettings, SettingsConfigDict
    USE_PYDANTIC_SETTINGS = True
except ImportError:
    # Fallback nếu không có pydantic_settings
    USE_PYDANTIC_SETTINGS = False
    from dotenv import load_dotenv
    # Load biến môi trường từ file .env
    load_dotenv()

# Định nghĩa Settings class dựa trên việc có pydantic_settings hay không
if USE_PYDANTIC_SETTINGS:
    class Settings(BaseSettings):
        """
        Cấu hình ứng dụng, đọc từ biến môi trường hoặc file .env.
        """
        # Cấu hình Database (sử dụng str để chấp nhận cả PostgreSQL và SQLite)
        DATABASE_URL: str = Field(..., validation_alias="DATABASE_URL")

        # API Key cho OpenAI
        OPENAI_API_KEY: str = Field(..., validation_alias="OPENAI_API_KEY")

        # (Tùy chọn) Tên model ChatGPT muốn sử dụng
        CHATGPT_MODEL: str = "gpt-4o" # Hoặc gpt-4, ...

        # (Tùy chọn) Số lượng message tối đa lấy làm context lịch sử chat
        MAX_HISTORY_MESSAGES: int = 20

        # Bỏ qua xác thực trong môi trường phát triển
        SKIP_AUTH_FOR_TESTS: bool = Field(False, validation_alias="SKIP_AUTH_FOR_TESTS")

        # Rivalz service settings
        RIVAL_URL: str = Field(default="https://api.rivalz.ai", validation_alias="RIVAL_URL")
        RIVAL_AUTH_KEY: Optional[str] = Field(None, validation_alias="RIVAL_AUTH_KEY")
        USE_RIVALZ_SERVICE: bool = Field(False, validation_alias="USE_RIVALZ_SERVICE")
        SESSION_TIMEOUT_MINUTES: int = Field(60, validation_alias="SESSION_TIMEOUT_MINUTES")
        RIVALZ_PROJECT_ID: str = Field("default_project_id", validation_alias="RIVALZ_PROJECT_ID")

        # Cấu hình cho Pydantic v2
        model_config = SettingsConfigDict(
            env_file=".env",
            env_file_encoding="utf-8",
            extra="ignore"
        )
else:
    # Fallback khi không có pydantic_settings
    class Settings(BaseModel):
        """
        Cấu hình ứng dụng, đọc trực tiếp từ biến môi trường.
        """
        # Cấu hình Database (sử dụng str để chấp nhận cả PostgreSQL và SQLite)
        DATABASE_URL: str = Field(default_factory=lambda: os.getenv("DATABASE_URL"))

        # API Key cho OpenAI
        OPENAI_API_KEY: str = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))

        # (Tùy chọn) Tên model ChatGPT muốn sử dụng
        CHATGPT_MODEL: str = "gpt-4o" # Hoặc gpt-4, ...

        # (Tùy chọn) Số lượng message tối đa lấy làm context lịch sử chat
        MAX_HISTORY_MESSAGES: int = 20

        # Bỏ qua xác thực trong môi trường phát triển
        SKIP_AUTH_FOR_TESTS: bool = Field(
            default_factory=lambda: os.getenv("SKIP_AUTH_FOR_TESTS", "False").lower() == "true"
        )

        # Add these to the Settings class
        RIVAL_URL: str = Field(default_factory=lambda: os.getenv("RIVAL_URL", "https://api.rivalz.ai"))
        RIVAL_AUTH_KEY: str = Field(default_factory=lambda: os.getenv("RIVAL_AUTH_KEY"))
        USE_RIVALZ_SERVICE: bool = os.getenv("USE_RIVALZ_SERVICE", "True").lower() in ("true", "1", "t")
        SESSION_TIMEOUT_MINUTES: int = int(os.getenv("SESSION_TIMEOUT_MINUTES", "60"))
        RIVALZ_PROJECT_ID: str = os.getenv("RIVALZ_PROJECT_ID", "default_project_id")
# Tạo một instance của Settings để sử dụng trong toàn bộ ứng dụng
settings = Settings()
