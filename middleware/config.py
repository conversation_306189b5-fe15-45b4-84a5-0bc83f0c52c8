# Configuration settings for the application
import os
from typing import Optional

# Thử import pydantic_settings trước
try:
    from pydantic_settings import BaseSettings, SettingsConfigDict

    class _Settings(BaseSettings):
        """
        C<PERSON>u hình ứng dụng, đọc từ biến môi trường hoặc file .env.
        """
        # Cấu hình Database (sử dụng str để chấp nhận cả PostgreSQL và SQLite)
        DATABASE_URL: str = ""

        # API Key cho OpenAI
        OPENAI_API_KEY: str = ""

        # (Tùy chọn) Tên model ChatGPT muốn sử dụng
        CHATGPT_MODEL: str = "gpt-4o"  # Hoặc gpt-4, ...

        # (Tùy chọn) Số lượng message tối đa lấy làm context lịch sử chat
        MAX_HISTORY_MESSAGES: int = 20

        # Bỏ qua xác thực trong môi trường phát triển
        SKIP_AUTH_FOR_TESTS: bool = False

        # Rivalz service settings
        RIVAL_URL: str = "https://api.rivalz.ai"
        RIVAL_AUTH_KEY: Optional[str] = None
        USE_RIVALZ_SERVICE: bool = False
        SESSION_TIMEOUT_MINUTES: int = 60
        RIVALZ_PROJECT_ID: str = "default_project_id"

        # Cấu hình cho Pydantic v2
        model_config = SettingsConfigDict(
            env_file=".env",
            env_file_encoding="utf-8",
            extra="ignore"
        )

    # Alias để sử dụng
    Settings = _Settings  # type: ignore

except ImportError:
    # Fallback nếu không có pydantic_settings
    from pydantic import BaseModel

    try:
        from dotenv import load_dotenv
        # Load biến môi trường từ file .env
        load_dotenv()
    except ImportError:
        pass  # dotenv không có sẵn

    class Settings(BaseModel):
        """
        Cấu hình ứng dụng, đọc trực tiếp từ biến môi trường.
        """
        # Cấu hình Database (sử dụng str để chấp nhận cả PostgreSQL và SQLite)
        DATABASE_URL: str = os.getenv("DATABASE_URL", "")

        # API Key cho OpenAI
        OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

        # (Tùy chọn) Tên model ChatGPT muốn sử dụng
        CHATGPT_MODEL: str = "gpt-4o"  # Hoặc gpt-4, ...

        # (Tùy chọn) Số lượng message tối đa lấy làm context lịch sử chat
        MAX_HISTORY_MESSAGES: int = 20

        # Bỏ qua xác thực trong môi trường phát triển
        SKIP_AUTH_FOR_TESTS: bool = os.getenv("SKIP_AUTH_FOR_TESTS", "False").lower() == "true"

        # Rivalz service settings
        RIVAL_URL: str = os.getenv("RIVAL_URL", "https://api.rivalz.ai")
        RIVAL_AUTH_KEY: Optional[str] = os.getenv("RIVAL_AUTH_KEY")
        USE_RIVALZ_SERVICE: bool = os.getenv("USE_RIVALZ_SERVICE", "False").lower() in ("true", "1", "t")
        SESSION_TIMEOUT_MINUTES: int = int(os.getenv("SESSION_TIMEOUT_MINUTES", "60"))
        RIVALZ_PROJECT_ID: str = os.getenv("RIVALZ_PROJECT_ID", "default_project_id")

# Tạo một instance của Settings để sử dụng trong toàn bộ ứng dụng
settings = Settings()
