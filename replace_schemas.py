#!/usr/bin/env python3
"""Script to replace 'schemas.' with 'dtos.' in files"""

import os
import re

def replace_in_file(file_path):
    """Replace schemas. with dtos. in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace schemas. with dtos.
        new_content = content.replace('schemas.', 'dtos.')
        
        if content != new_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ Updated {file_path}")
            return True
        else:
            print(f"⏭️  No changes needed in {file_path}")
            return False
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    files_to_update = [
        'controllers/routes.py',
        'controllers/chat_routes.py', 
        'controllers/chat_helpers.py',
        'db/async_crud.py'
    ]
    
    updated_count = 0
    for file_path in files_to_update:
        if os.path.exists(file_path):
            if replace_in_file(file_path):
                updated_count += 1
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print(f"\n🎉 Updated {updated_count} files")

if __name__ == "__main__":
    main()
