#!/usr/bin/env python3
"""Test script for async_crud.py"""

try:
    from db import async_crud
    from dtos import UserCreate, StepCreate
    print("✅ async_crud imported successfully!")
    
    # Test DTO creation
    user_dto = UserCreate(identifier="test_user", name="Test User")
    step_dto = StepCreate(name="Test Step", type="USER_MESSAGE", input="Hello")
    
    print(f"✅ UserCreate DTO: {user_dto}")
    print(f"✅ StepCreate DTO: {step_dto}")
    
    # Test model_dump method
    step_dict = step_dto.model_dump(exclude_unset=True)
    print(f"✅ model_dump works: {step_dict}")
    
    print("✅ All async_crud dependencies working correctly!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
