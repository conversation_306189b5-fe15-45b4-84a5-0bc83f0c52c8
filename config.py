# Configuration settings for the application
import os
from typing import Optional
from pydantic import Field, BaseModel
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    """
    C<PERSON>u hình <PERSON>ng dụng, đọc từ biến môi trường hoặc file .env.
    """
    # Cấu hình Database (sử dụng str để chấp nhận cả PostgreSQL và SQLite)
    DATABASE_URL: str = Field(..., validation_alias="DATABASE_URL")

    # API Key cho OpenAI
    OPENAI_API_KEY: str = Field(..., validation_alias="OPENAI_API_KEY")

    # (Tùy chọn) Tên model ChatGPT muốn sử dụng
    CHATGPT_MODEL: str = "gpt-4o"  # Hoặc gpt-4, ...

    # (Tùy chọn) Số lượng message tối đa lấy làm context lịch sử chat
    MAX_HISTORY_MESSAGES: int = 20

    # Bỏ qua xác thực trong môi trường phát triển
    SKIP_AUTH_FOR_TESTS: bool = Field(False, validation_alias="SKIP_AUTH_FOR_TESTS")

    # Rivalz service settings
    RIVAL_URL: str = Field(default="https://api.rivalz.ai", validation_alias="RIVAL_URL")
    RIVAL_AUTH_KEY: Optional[str] = Field(None, validation_alias="RIVAL_AUTH_KEY")
    USE_RIVALZ_SERVICE: bool = Field(False, validation_alias="USE_RIVALZ_SERVICE")
    SESSION_TIMEOUT_MINUTES: int = Field(60, validation_alias="SESSION_TIMEOUT_MINUTES")
    RIVALZ_PROJECT_ID: str = Field("default_project_id", validation_alias="RIVALZ_PROJECT_ID")

    # Cấu hình cho Pydantic v2
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )
# Tạo một instance của Settings để sử dụng trong toàn bộ ứng dụng
settings = Settings()
