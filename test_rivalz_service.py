#!/usr/bin/env python3
"""Test script for rivalz_service.py"""

try:
    from services.rivalz_service import (
        convert_to_conversation_messages,
        extract_content_from_response,
        format_history_for_prompt
    )
    print("✅ rivalz_service imported successfully!")
    
    # Test convert_to_conversation_messages
    test_messages = [
        {"role": "user", "content": "Hello"},
        {"role": "assistant", "content": "Hi there!"}
    ]
    result = convert_to_conversation_messages(test_messages)
    print(f"✅ convert_to_conversation_messages works: {len(result)} items")
    
    # Test format_history_for_prompt
    test_history = [
        {"type": "USER_MESSAGE", "input": "Hello"},
        {"type": "AI_RESPONSE", "output": "Hi there!"}
    ]
    formatted = format_history_for_prompt(test_history)
    print(f"✅ format_history_for_prompt works: {len(formatted)} items")
    
    # Test extract_content_from_response with None
    content = extract_content_from_response(None)
    print(f"✅ extract_content_from_response handles None: {content}")
    
    print("✅ All rivalz_service functions working correctly!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
