#!/bin/bash
set -e

# Chờ PostgreSQL khởi động
echo "Waiting for PostgreSQL to start..."
# Sử dụng netcat để kiểm tra kết nối đến PostgreSQL
for i in {1..30}; do
    if nc -z host.docker.internal 5432; then
        echo "PostgreSQL started"
        break
    fi
    echo "Waiting for PostgreSQL... $i/30"
    sleep 2
done

# Khởi tạo database
echo "Initializing database..."
python init_async_db.py || {
    echo "Database initialization failed, but continuing..."
}

# Khởi động ứng dụng
echo "Starting application..."
exec "$@"
