"""
Helper functions for chat processing.
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from .. import schemas
from ..db import models, async_crud
from ..services import chatgpt_service
from ..utils.helpers import parse_agent_response
from ..middleware.cache import set_cache

# Configure logging
logger = logging.getLogger(__name__)

def sqlalchemy_to_dict(obj) -> Dict[str, Any]:
    """Convert SQLAlchemy model to dict, handling special fields."""
    import json
    import datetime
    from sqlalchemy import MetaData

    result = {}
    for key in obj.__dict__:
        if key.startswith('_'):
            continue
        value = getattr(obj, key)

        # Handle special fields
        if key == 'metadata_':
            # Ensure metadata is a dict
            try:
                if value is None:
                    result['metadata'] = {}
                elif isinstance(value, dict):
                    result['metadata'] = dict(value)
                elif isinstance(value, MetaData):
                    # Handle SQLAlchemy MetaData objects
                    result['metadata'] = {}
                elif hasattr(value, '__dict__'):
                    # If it's an object with __dict__, convert to dict
                    try:
                        result['metadata'] = {k: v for k, v in value.__dict__.items() if not k.startswith('_')}
                    except:
                        result['metadata'] = {}
                else:
                    # Try to convert to dict if it's a JSON string
                    try:
                        if isinstance(value, str):
                            result['metadata'] = json.loads(value)
                        else:
                            result['metadata'] = {}
                    except:
                        result['metadata'] = {}
            except:
                result['metadata'] = {}
        elif key == 'tags':
            # Ensure tags is a list
            try:
                if value is None:
                    result['tags'] = []
                elif isinstance(value, list):
                    result['tags'] = list(value)
                elif hasattr(value, '__iter__') and not isinstance(value, (str, dict)):
                    # If it's iterable but not a string or dict
                    result['tags'] = list(value)
                else:
                    # Try to convert to list if it's a JSON string
                    try:
                        if isinstance(value, str):
                            result['tags'] = json.loads(value)
                        else:
                            result['tags'] = []
                    except:
                        result['tags'] = []
            except:
                result['tags'] = []
        elif isinstance(value, datetime.datetime):
            # Convert datetime to ISO format string
            result[key] = value.isoformat()
        elif isinstance(value, (datetime.date, datetime.time)):
            # Convert date and time to ISO format string
            result[key] = value.isoformat()
        else:
            result[key] = value

    return result

async def process_rivalz_content(
    extracted_contents_list: Optional[List[str]],
    agent_response_obj: Any,
    user_step_model: models.Step,
    thread_id: str,
    db: AsyncSession
) -> List[schemas.StepResponse]:
    """Process extracted content from Rivalz response."""
    generated_ai_steps: List[schemas.StepResponse] = []
    
    if extracted_contents_list:
        for index, raw_agent_response_str in enumerate(extracted_contents_list):
            if not raw_agent_response_str or not raw_agent_response_str.strip():
                logger.warning(f"Skipping empty raw agent response at index {index} for thread {thread_id}.")
                continue

            parsed_agent_name, parsed_content = parse_agent_response(raw_agent_response_str)
            step_specific_metadata: Dict[str, Any] = {}

            step_name: str
            if parsed_agent_name:
                step_specific_metadata['source_agent_name'] = parsed_agent_name
                step_name = f"Response from {parsed_agent_name}"
            else:
                step_name = f"AI Response Part {index + 1} (Unknown Source)"
                step_specific_metadata['source_agent_name'] = "Unknown"
                
            ai_step_data = schemas.StepCreate(
                name=step_name,
                type="AI_RESPONSE",
                output=parsed_content,
                parentId=str(user_step_model.id),
                metadata=step_specific_metadata
            )
            
            logger.info(f"Creating AI step for thread {thread_id} with metadata: {step_specific_metadata}")
            logger.info(f"Creating AI step for thread {thread_id} with content: {parsed_content[:100]}...")
            
            db_ai_step = await async_crud.create_step(db=db, step_data=ai_step_data, thread_id=thread_id)
            step_dict = sqlalchemy_to_dict(db_ai_step)
            generated_ai_steps.append(schemas.StepResponse(**step_dict))
        
        if not generated_ai_steps and extracted_contents_list:
            logger.warning(f"Extracted content list was not empty but no steps were generated for thread {thread_id}.")
            # Tạo một step thông báo
            info_step_data = schemas.StepCreate(
                name="AI Processing Info", 
                type="AI_RESPONSE", 
                output="AI processed the request, but individual parts were empty.",
                parentId=str(user_step_model.id),
                metadata={"info": "all_extracted_parts_empty"}
            )
            db_info_step = await async_crud.create_step(db=db, step_data=info_step_data, thread_id=thread_id)
            step_dict = sqlalchemy_to_dict(db_info_step)
            generated_ai_steps.append(schemas.StepResponse(**step_dict))

    elif extracted_contents_list == []:
        logger.info(f"Rivalz: extract_content_from_response returned an empty list for thread {thread_id}.")
        no_content_output = "Agent processed the request but returned no specific textual parts."
        no_content_step_data = schemas.StepCreate(
            name="AI Response Info (Rivalz)", 
            type="AI_RESPONSE", 
            output=no_content_output,
            parentId=str(user_step_model.id),
            metadata={"info": "rivalz_empty_extracted_content_list"}
        )
        db_no_content_step = await async_crud.create_step(db=db, step_data=no_content_step_data, thread_id=thread_id)
        step_dict = sqlalchemy_to_dict(db_no_content_step)
        generated_ai_steps.append(schemas.StepResponse(**step_dict))
    else:
        logger.error(f"Rivalz: extract_content_from_response returned None for thread {thread_id}.")
        extraction_error_output = "Sorry, there was an error processing the AI's response format."
        error_step_data = schemas.StepCreate(
            name="AI Extraction Error (Rivalz)", 
            type="ERROR", 
            output=extraction_error_output,
            parentId=str(user_step_model.id),
            metadata={"error": "rivalz_extraction_returned_none"}
        )
        db_extraction_error_step = await async_crud.create_step(db=db, step_data=error_step_data, thread_id=thread_id)
        step_dict = sqlalchemy_to_dict(db_extraction_error_step)
        generated_ai_steps.append(schemas.StepResponse(**step_dict))

    return generated_ai_steps

async def handle_chatgpt_response(
    user_message_content: str,
    user_step_model: models.Step,
    history_steps_models: List[models.Step],
    thread_id: str,
    db: AsyncSession,
    current_user: models.User
) -> List[schemas.StepResponse]:
    """Handle ChatGPT service response processing."""
    logger.info("Using ChatGPT service for AI response.")
    generated_ai_steps: List[schemas.StepResponse] = []
    
    history_for_prompt_gpt = chatgpt_service.format_history_for_prompt(history_steps_models)
    prompt_messages_gpt = history_for_prompt_gpt + [{"role": "user", "content": user_message_content}]

    ai_raw_response_dict = await chatgpt_service.get_chatgpt_response(prompt_messages_gpt)
    
    step_name_gpt = "AI Response (ChatGPT)"
    step_type_gpt = "AI_RESPONSE"
    final_content_gpt = ""
    gpt_metadata: Dict[str, Any] = {}

    if ai_raw_response_dict:
        extracted_gpt_content = chatgpt_service.extract_content_from_response(ai_raw_response_dict)
        if extracted_gpt_content:
            final_content_gpt = extracted_gpt_content
            if "usage" in ai_raw_response_dict:
                gpt_metadata["openai_response_usage"] = ai_raw_response_dict.get("usage", {})
        else:
            logger.error(f"ChatGPT: Could not extract content from response for thread {thread_id}.")
            final_content_gpt = "Sorry, I received an invalid response format from the AI (ChatGPT)."
            step_type_gpt = "ERROR"
            gpt_metadata["error"] = "chatgpt_extraction_failed"
    else:
        logger.error(f"ChatGPT: No response received from AI API for thread {thread_id}.")
        final_content_gpt = "Sorry, I cannot connect to the AI (ChatGPT) at this time."
        step_type_gpt = "ERROR"
        gpt_metadata["error"] = "chatgpt_service_no_response"

    gpt_step_data = schemas.StepCreate(
        name=step_name_gpt,
        type=step_type_gpt,
        output=final_content_gpt,
        parentId=str(user_step_model.id),
        metadata=gpt_metadata
    )
    db_gpt_step = await async_crud.create_step(db=db, step_data=gpt_step_data, thread_id=thread_id)
    step_dict = sqlalchemy_to_dict(db_gpt_step)
    generated_ai_steps.append(schemas.StepResponse(**step_dict))

    return generated_ai_steps

async def invalidate_cache(thread_id: str, user_id: str):
    """Invalidate cache for thread and related entries."""
    try:
        cache_keys_to_invalidate = [
            f"thread:{thread_id}:user:{user_id}",
            f"user_threads:{user_id}:skip=0:limit=100"
        ]
        # Thêm các key cache cho steps của thread
        for s_offset in [0, 10, 20]:
            for l_limit in [10, 20, 50, 100]:
                cache_keys_to_invalidate.append(f"thread_steps:{thread_id}:skip={s_offset}:limit={l_limit}")
        
        for key in cache_keys_to_invalidate:
            await set_cache(key, None, expire=1)  # Expire ngay lập tức

        logger.info(f"Invalidated cache for thread '{thread_id}' and related entries.")
    except Exception as e:
        logger.warning(f"Failed to invalidate cache for thread {thread_id}: {e}", exc_info=True)
