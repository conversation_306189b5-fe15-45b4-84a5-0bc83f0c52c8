#!/usr/bin/env python3
"""Fix async_crud.py to use dtos instead of schemas"""

import re

def fix_async_crud():
    file_path = 'db/async_crud.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace schemas. with dtos.
    new_content = content.replace('schemas.', 'dtos.')
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ Fixed async_crud.py")

if __name__ == "__main__":
    fix_async_crud()
