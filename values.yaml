replicaCount: 1
type: app # There are app, worker, cronjob
statefulset: false 
nameOverride: "ragent-be"

image:
  repository: asia-southeast1-docker.pkg.dev/rivalz-be/rome/ragent-be
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "staging-latest"
  containerPort: 9000
terminationGracePeriodSeconds: 60
imagePullSecrets: 
  - name: gar-registry

secretMounts:
  - name: env
    secretName: api-secrets
    path: /app/.env
    subPath: .env
service:
  type: ClusterIP
  port: 3000
extraEnvs: []
envFrom: []

podAnnotations: {}
livenessProbe: []
readinessProbe: []

resources:
  limits:
    cpu: 1
    memory: "2Gi"
  requests: 
    cpu: "500m"
    memory: "1Gi"

ingress:
  enabled: true
  kind: gateway ### with 2 values: gateway and ingress
  gateway: ingressgateway
  gatewayname: istio-system/rivalz-ai
  hosts: 
    - 'api-orchestration.rivalz.ai'
  servers: 
    - port: 3000
      name: http
      protocol: HTTP

  uri_prefix: "/" ### Please mention ending slash
  # uri_rewrite: "/"