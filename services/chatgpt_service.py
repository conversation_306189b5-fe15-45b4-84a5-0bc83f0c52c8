from openai import Async<PERSON>penAI
from typing import List, Dict, Optional
import logging

from config import settings
from ..db import models

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the OpenAI client with the API key from settings
try:
    client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    logger.info("AsyncOpenAI client initialized successfully.")
except Exception as e:
    logger.error(f"Failed to initialize AsyncOpenAI client: {e}", exc_info=True)
    client = None

# System prompt for RunX Healthcare Chatbot


RUNX_HEALTHCARE_SYSTEM_PROMPT = [{
    "role": "system",
    "content": """
        You are Helpful Agent, an AI assistant specializing in health, nutrition, exercise, sports, and healthy lifestyle topics.

## 🔹 Requirements & Guidelines:
1️⃣ **Provide Specific & Detailed Answers**:
   - For all questions related to health, exercise, or sports, answer as specifically, lenght more than 1000 words and step-by-step as possible.
   - Give clear, practical, and actionable instructions.
   - Whenever possible, include illustrative examples, important notes, or useful tips.

2️⃣ **Use the Web Search Tool**:
   - If the question requires high accuracy, scientific information, or up-to-date health data, use the web_search_preview tool to look up and provide the most accurate information.
   - Especially for questions guiding users on a health or fitness topic, or when users ask about specific knowledge, you must always use the web_search_preview tool to search for information before answering.
   - Always prioritize reputable, up-to-date, and well-cited sources.

3️⃣ **Limitations & Notes**:
   - You are not a doctor and do not diagnose diseases or prescribe medication.
   - If users ask about emergencies or specific medical conditions, advise them to contact a healthcare professional.

4️⃣ **Encourage a Healthy Lifestyle**:
   - Always motivate users to maintain good habits: balanced diet, regular exercise, sufficient sleep, and stress management.
   - Provide practical and easy-to-implement advice.

5️⃣ **Handle Off-topic Questions**:
   - If the question is not related to health, respond briefly and politely, and guide the user back to health topics.

6️⃣ **Respect & Privacy**:
   - Maintain a professional, supportive, and respectful attitude.
   - Do not store or share personal information, and do not provide personalized medical advice.

## 🔹 Examples:
✅ "Step-by-step guide to improve sleep quality?"
✅ "How to build a workout routine for beginners?"
✅ "What are the steps to prepare a healthy daily meal?"

❌ "Which field should I invest in?" → "I specialize in health topics. For financial advice, please consult a finance expert."
❌ "Can you diagnose my symptoms?" → "I'm not a doctor. Please see a healthcare professional for accurate advice."
    """
}]

def format_history_for_prompt(steps: List[models.Step]) -> List[Dict[str, str]]:
    # Start with the system prompt in correct format
    prompt_history = [RUNX_HEALTHCARE_SYSTEM_PROMPT[0]]  # Get the first (and only) dict from the list

    # Add conversation history
    for step in steps:
        step_type = getattr(step, 'type', '')
        step_input = getattr(step, 'input', None)
        step_output = getattr(step, 'output', None)

        if step_type == "USER_MESSAGE" and step_input is not None:
            input_content = str(step_input).strip()
            if input_content:
                prompt_history.append({"role": "user", "content": input_content})
        elif step_type == "AI_RESPONSE" and step_output is not None:
            output_content = str(step_output).strip()
            if output_content:
                prompt_history.append({"role": "assistant", "content": output_content})

    return prompt_history

async def get_chatgpt_response(prompt_messages: List[Dict[str, str]]) -> Optional[Dict]:
    """
    Call the OpenAI API to get a response from the ChatGPT model.

    Args:
        prompt_messages: List of messages in the format expected by the OpenAI API.

    Returns:
        The complete response from the API as a dictionary, or None if there was an error.
    """
    if not client:
        logger.error("OpenAI client not initialized. Cannot call API.")
        return None

    try:
        logger.info(f"Calling OpenAI API with model: {settings.CHATGPT_MODEL}")
        response = await client.chat.completions.create(
            model=settings.CHATGPT_MODEL,
            messages=prompt_messages,  # type: ignore
            temperature=0.7,
            max_tokens=4096,
            timeout=30,
        )
        logger.info("Received response from OpenAI API.")

        # Convert the response object to a dictionary
        response_dict = response.model_dump()
        return response_dict

    except Exception as e:
        logger.error(f"Error calling OpenAI API: {e}", exc_info=True)
        return None

def extract_content_from_response(response: Optional[Dict]) -> Optional[str]:
    """
    Extract the text content from the OpenAI API response (new format).

    Args:
        response: The response dictionary from the OpenAI API.

    Returns:
        The extracted text content, or None if extraction failed.
    """
    if not response:
        return None
    try:
        # Extract content from the response dictionary
        if isinstance(response, dict) and "choices" in response:
            choices = response.get("choices", [])
            if choices and len(choices) > 0:
                message = choices[0].get("message", {})
                content = message.get("content")
                if content:
                    return content.strip()

        logger.warning(f"Could not extract content from response: {response}")
        return None
    except (IndexError, KeyError, AttributeError) as e:
        logger.error(f"Error extracting content: {e}. Response: {response}", exc_info=True)
        return None

async def test_openai_api():
    """
    Test function to verify the OpenAI API connection and response.

    Returns:
        The response from the API as a string, or an error message.
    """
    test_message = [
        RUNX_HEALTHCARE_SYSTEM_PROMPT[0],  # Get the dict from the list
        {"role": "user", "content": "Hello, can you give me a short response about healthy sleep habits?"}
    ]

    try:
        response_dict = await get_chatgpt_response(test_message)
        content = extract_content_from_response(response_dict)

        if content:
            logger.info(f"API test successful. Response: {content}")
            return f"API test successful. Response: {content}"
        else:
            logger.error("API test failed: Could not extract content from response")
            return "API test failed: Could not extract content from response"
    except Exception as e:
        error_msg = f"API test failed with error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return error_msg
