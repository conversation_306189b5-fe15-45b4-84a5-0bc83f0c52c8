"""
Service for interacting with Rivalz agents.

This module provides functions for interacting with Rivalz agents,
including converting message formats and handling responses.
"""

import logging
import re
from typing import List, Dict, Optional, Any

from rAgent.agents import AgentResponse, AgentProcessingResult
from rAgent.types import ConversationMessage, ParticipantRole
from ..utils.rivalz_session_manager import rivalz_session_manager

# Configure logging
logger = logging.getLogger(__name__)


def convert_to_conversation_messages(messages: List[Dict[str, str]]) -> List[ConversationMessage]:  # type: ignore
    """
    Convert a list of message dictionaries to ConversationMessage objects.

    Args:
        messages: List of message dictionaries with 'role' and 'content' keys

    Returns:
        List of ConversationMessage objects
    """
    result = []
    for msg in messages:
        role = msg.get("role", "user")
        content = msg.get("content", "")

        # Map role string to ParticipantRole enum
        if role == "system":
            participant_role = ParticipantRole.SYSTEM
        elif role == "assistant":
            participant_role = ParticipantRole.ASSISTANT
        else:
            participant_role = ParticipantRole.USER

        # Create ConversationMessage object
        conv_msg = ConversationMessage(role=participant_role, content=[{'text': content}])  # type: ignore
        result.append(conv_msg)

    return result


def extract_content_from_response(agent_response_obj: Optional[Any]) -> Optional[List[str]]:  # type: ignore
    """
    Extracts content from an AgentResponse object.
    If the content contains <startagent>...</endagent> blocks, it extracts
    the content from within these blocks. Otherwise, it returns the
    primary text content.
    """
    if not agent_response_obj or not agent_response_obj.output:
        logger.info("extract_content_from_response: No agent_response_obj or agent_response_obj.output is None/empty.")
        return None

    final_raw_text: Optional[str] = None

    # Use getattr to safely access attributes
    output = getattr(agent_response_obj, 'output', None)
    if hasattr(output, 'content'):
        content = getattr(output, 'content', None)
        if isinstance(content, str):
            final_raw_text = content
            logger.info(f"Extracted raw text from ConversationMessage.content (string): '{final_raw_text[:100]}...'")
        elif isinstance(content, list):
            if content:
                first_item = content[0]
                if isinstance(first_item, dict):
                    final_raw_text = first_item.get('text')
                    logger.info(f"Extracted raw text from ConversationMessage.content[0]['text'] (dict): '{str(final_raw_text)[:100]}...'")
                elif hasattr(first_item, 'text'):
                    text_attr = getattr(first_item, 'text', None)
                    if isinstance(text_attr, str) or text_attr is None:
                         final_raw_text = text_attr
                         logger.info(f"Extracted raw text from ConversationMessage.content[0].text (object): '{str(final_raw_text)[:100]}...'")
                    else:
                        logger.warning(f"ConversationMessage.content[0].text is not a string or None: {type(text_attr)}")
                else:
                    logger.warning(f"ConversationMessage.content[0] is not a dict with 'text' or an object with 'text': {type(first_item)}")
            else:
                logger.info("ConversationMessage.content is an empty list.")
        else:
            logger.warning(f"ConversationMessage.content is an unexpected type: {type(content)}")

    elif isinstance(output, str):
        final_raw_text = output
        logger.info(f"Extracted raw text directly from AgentResponse.output (string): '{final_raw_text[:100]}...'")
    else:
        logger.warning(f"AgentResponse.output is an unexpected type: {type(output)}")

    if final_raw_text is None:
        logger.info("extract_content_from_response: final_raw_text is None before regex.") # Changed to INFO
        return None
    if not final_raw_text.strip():
        logger.info("extract_content_from_response: final_raw_text is empty or all whitespace before regex.") # Changed to INFO
        return None

    logger.info(f"Attempting regex on final_raw_text: '{final_raw_text}'")
    try:
        # Regex to find content within <startagent>...</endagent> or <\startagent>...<endagent>
        # Opening tag allows an optional backslash.
        # Closing tag is literally <endagent>.
        regex_pattern = r"<\\?startagent>(.*?)<endagent>" # MODIFIED closing tag
        extracted_blocks = re.findall(regex_pattern, final_raw_text, re.DOTALL)
        
        logger.info(f"Regex pattern used: '{regex_pattern}'")
        logger.info(f"Found {len(extracted_blocks)} blocks: {extracted_blocks}")

        if extracted_blocks:
            processed_blocks = [block.strip() for block in extracted_blocks if block.strip()]
            logger.info(f"Processed {len(processed_blocks)} non-empty blocks: {processed_blocks}")
            return processed_blocks
        else:
            logger.info("No agent tags found by regex. Returning full stripped text as a single item list.")
            return [final_raw_text.strip()]
    except Exception as e:
        logger.error(f"Error during regex processing: {e} for text '{final_raw_text[:100]}...'", exc_info=True)
        return None



def format_history_for_prompt(history_steps: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """
    Format chat history steps for use in the prompt.

    Args:
        history_steps: List of chat history steps from the database

    Returns:
        List of message dictionaries in the format expected by the Rivalz agent
    """
    formatted_history = []
    logger.info(f"Formatting {len(history_steps)} history steps for prompt.")
    
    for i, step in enumerate(history_steps):
        try:
            # Kiểm tra type an toàn
            step_type = step.get("type", "") if isinstance(step, dict) else ""
            
            if step_type == "USER_MESSAGE":
                content = step.get("input", "")
                if content:
                    formatted_history.append({
                        "role": "user",
                        "content": content
                    })
                    logger.debug(f"Added USER message {i}: '{content[:50]}...'")
                    
            elif step_type == "AI_RESPONSE":  # ✅ Sửa từ "ASSISTANT_MESSAGE" thành "AI_RESPONSE"
                content = step.get("output", "")
                if content:
                    formatted_history.append({
                        "role": "assistant",
                        "content": content
                    })
                    logger.debug(f"Added ASSISTANT message {i}: '{content[:50]}...'")
            else:
                logger.debug(f"Skipped step {i} with type: '{step_type}'")
                
        except Exception as e:
            logger.error(f"Error processing history step {i}: {e}, step: {step}")
            continue

    logger.info(f"Formatted {len(formatted_history)} messages from {len(history_steps)} steps")
    return formatted_history

async def get_rivalz_response(user_id: str, thread_id: str, prompt_messages: List[Dict[str, str]], project_id: str, agent_type_selected: str = "rx") -> Optional[Any]:  # type: ignore
    try:
        agent = await rivalz_session_manager.get_or_create_agent(project_id)
        if not agent: # Kiểm tra nếu agent không được tạo
            logger.error(f"Failed to get or create agent for project {project_id}")
            return None # Hoặc trả về một AgentResponse lỗi cụ thể

        # Kiểm tra xem agent có được khởi tạo thành công không (ví dụ, có các sub-agents không)
        # (Điều này phụ thuộc vào logic khởi tạo của RivalzAgent)
        # Ví dụ: if agent_type_selected == "rc" and not agent.rc_agent: #... xử lý lỗi

        conversation_messages = convert_to_conversation_messages(prompt_messages[:-1])
        user_input = prompt_messages[-1]["content"] if prompt_messages else ""

        logger.info(f"Calling Rivalz agent (type: {agent_type_selected}) for project {project_id}, user {user_id}, thread {thread_id}")
        # Process the request
        agent_response_obj = await agent.process_request( # Đổi tên biến để rõ ràng
            input_text=user_input,
            user_id=user_id,
            session_id=thread_id,
            chat_history=conversation_messages,
            additional_params={'project_id': project_id, 'team_info': agent.team_info}, # Có thể thêm các params khác nếu cần
            agent_type=agent_type_selected
        )

        rivalz_session_manager.update_last_active(project_id)
        logger.info(f"Received response from Rivalz agent for project {project_id}")

        return agent_response_obj # Trả về trực tiếp AgentResponse object

    except Exception as e:
        logger.error(f"Error calling Rivalz agent: {e}", exc_info=True)
        # Trả về một AgentResponse lỗi nếu cần
        error_metadata = AgentProcessingResult(
            user_input=prompt_messages[-1]["content"] if prompt_messages else "",
            agent_id="error_agent",
            agent_name="Error Agent",
            user_id=user_id,
            session_id=thread_id,
            additional_params={'error_message': str(e), 'project_id': project_id}
        )
        # Create error response using dynamic construction
        error_output = ConversationMessage(role=ParticipantRole.ASSISTANT, content=[{'text': f"Sorry, There is an error: {str(e)}"}])  # type: ignore
        return AgentResponse(  # type: ignore
            metadata=error_metadata,
            output=error_output,
            streaming=False
        )
    
# Cập nhật hàm test để truyền project_id
async def test_rivalz_api(project_id: str = "test_project_id"):
    """
    Test function to verify the Rivalz agent connection and response.

    Args:
        project_id: Project identifier to use for testing

    Returns:
        The response from the agent as a string, or an error message
    """
    test_message = [
        {"role": "user", "content": "Hello, can you give me a short response about healthy sleep habits?"}
    ]

    try:
        # Use a test user and thread ID
        test_user_id = "test_user"
        test_thread_id = "test_thread"

        response_dict = await get_rivalz_response(test_user_id, test_thread_id, test_message, project_id)
        content = extract_content_from_response(response_dict)

        if content:
            logger.info(f"API test successful for project {project_id}. Response: {content[:100]}...")
            return f"API test successful. Response: {content[:100]}..."
        else:
            logger.error(f"API test failed for project {project_id}: Could not extract content from response")
            return "API test failed: Could not extract content from response"

    except Exception as e:
        error_msg = f"API test failed with error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return error_msg