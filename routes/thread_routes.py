# /app/api/v1/endpoints.py
from fastapi import APIRouter, Depends, HTTPException, status, Response, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
import logging
import datetime

from .. import dtos
from ..db import models, async_crud
from ..db.async_database import get_async_db_dependency
from ..auth import get_current_user_async
from ..services import chatgpt_service, rivalz_service
from config import settings
from ..middleware.cache import get_cache, set_cache
from typing import Optional
from ..utils.helpers import parse_agent_response

# Import utility function
from ..utils.chat_helpers import sqlalchemy_to_dict


logger = logging.getLogger(__name__)
# Remove global security dependency to avoid conflicts with Swagger UI
router = APIRouter()

# Create a separate router for endpoints that don't require authentication
public_router = APIRouter()

# --- Public Endpoints ---
@public_router.get(
    "/status",
    summary="Check API status",
    tags=["Status"]
)
async def check_api_status():
    """
    Public endpoint that doesn't require authentication.
    Used to check if the API is running.
    """
    return {
        "status": "ok",
        "message": "API is running"
    }

@router.get(
    "/auth-debug",
    summary="Debug authentication token",
    tags=["Debug"]
)
async def debug_auth_token(
    request: Request,
    current_user: models.User = Depends(get_current_user_async)
):
    """
    Endpoint to debug authentication token issues.
    Returns detailed information about the token and authentication process.
    """
    # Get the authorization header
    auth_header = request.headers.get("Authorization", "")

    # Log the full authorization header
    logger.info(f"Full Authorization header: {auth_header}")

    # Extract token details safely
    token = ""
    if auth_header.lower().startswith("bearer "):
        token = auth_header[7:]  # Remove "Bearer " prefix

    # Get token details
    token_info = {
        "token_length": len(token),
        "token_prefix": token[:10] + "..." if len(token) > 10 else token,
        "token_suffix": "..." + token[-5:] if len(token) > 5 else token,
        "token_parts": len(token.split(".")),
        "is_jwt_format": len(token.split(".")) == 3
    }

    # Get user details
    user_info = {
        "id": current_user.id,
        "identifier": current_user.identifier,
        "created_at": current_user.createdAt.isoformat() if hasattr(current_user, "createdAt") else None
    }

    # Get request details
    request_info = {
        "method": request.method,
        "url": str(request.url),
        "client_host": request.client.host if request.client else None,
        "headers": {k: v for k, v in request.headers.items() if k.lower() not in ["authorization"]}
    }

    return {
        "status": "authenticated",
        "message": "Authentication successful",
        "token_info": token_info,
        "user_info": user_info,
        "request_info": request_info
    }

# --- Thread Endpoints ---
@router.post(
    "/threads/",
    response_model=dtos.ThreadResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new conversation thread",
    tags=["Threads"]
)
@router.post(
    "/threads",  # Add route without trailing slash
    response_model=dtos.ThreadResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new conversation thread",
    tags=["Threads"],
    include_in_schema=False  # Don't show duplicate in docs
)
async def create_thread_endpoint( # Renamed function to avoid duplication
    thread_data: dtos.ThreadCreate,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    logger.info(f"User '{current_user.identifier}' is creating a new thread.")
    try:
        db_thread = await async_crud.create_thread(
            db=db,
            thread_data=thread_data,
            user_id=str(current_user.id),
            user_identifier=str(current_user.identifier)
        )
        logger.info(f"Thread created with ID: {db_thread.id} for user: {current_user.identifier}")

        # Convert SQLAlchemy model to dict
        thread_dict = sqlalchemy_to_dict(db_thread)
        logger.info(f"Thread dict after conversion: {thread_dict}")

        # Cache the thread
        try:
            cache_key = f"thread:{db_thread.id}:user:{current_user.id}"
            await set_cache(cache_key, thread_dict, expire=300)
        except Exception as e:
            logger.warning(f"Failed to cache thread {db_thread.id}: {e}")

        return thread_dict
    except Exception as e:
        logger.error(f"Error creating thread for user {current_user.identifier}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Could not create thread")

@router.get(
    "/threads/",
    response_model=List[dtos.ThreadResponse],
    summary="Get list of user's threads (chat history)",
    tags=["Threads"]
)
@router.get(
    "/threads",  # Add route without trailing slash
    response_model=List[dtos.ThreadResponse],
    summary="Get list of user's threads (chat history)",
    tags=["Threads"],
    include_in_schema=False  # Don't show duplicate in docs
)
async def get_user_threads_endpoint( # Renamed function
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    logger.info(f"Getting threads for user '{current_user.identifier}' with skip={skip}, limit={limit}")

    # Tạo cache key
    cache_key = f"user_threads:{current_user.id}:skip={skip}:limit={limit}"

    # Thử lấy từ cache
    try:
        cached_data = await get_cache(cache_key)
        if cached_data:
            logger.info(f"Retrieved {len(cached_data)} threads from cache")
            return cached_data
    except Exception as e:
        logger.warning(f"Failed to get threads from cache: {e}")

    # Nếu không có trong cache, lấy từ database
    threads = await async_crud.get_threads_by_user(db, user_id=str(current_user.id), skip=skip, limit=limit)

    # Convert list of SQLAlchemy models to dicts
    thread_dicts = [sqlalchemy_to_dict(thread) for thread in threads]
    logger.info(f"Retrieved {len(thread_dicts)} threads from database")

    # Lưu vào cache (hết hạn sau 5 phút)
    try:
        await set_cache(cache_key, thread_dicts, expire=300)
        logger.info(f"Cached {len(thread_dicts)} threads")
    except Exception as e:
        logger.warning(f"Failed to cache threads: {e}")

    return thread_dicts

@router.get(
    "/threads/{thread_id}",
    response_model=dtos.ThreadResponse,
    summary="Get detailed information of a thread",
    tags=["Threads"]
)
async def get_thread_endpoint( # Renamed function
    thread_id: str,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    logger.info(f"Getting thread '{thread_id}' for user '{current_user.identifier}'")

    # Tạo cache key
    cache_key = f"thread:{thread_id}:user:{current_user.id}"

    # Thử lấy từ cache
    try:
        cached_data = await get_cache(cache_key)
        if cached_data:
            logger.info(f"Retrieved thread '{thread_id}' from cache")
            return cached_data
    except Exception as e:
        logger.warning(f"Failed to get thread from cache: {e}")

    # Nếu không có trong cache, lấy từ database
    db_thread = await async_crud.get_thread(db, thread_id=thread_id)
    if db_thread is None:
        logger.warning(f"Thread '{thread_id}' not found.")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Thread not found")
    if str(db_thread.userId) != str(current_user.id):
        logger.warning(f"User '{current_user.identifier}' forbidden from accessing thread '{thread_id}'")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not allowed to access this thread")

    # Convert SQLAlchemy model to dict
    thread_dict = sqlalchemy_to_dict(db_thread)
    logger.info(f"Thread dict after conversion: {thread_dict}")

    # Lưu vào cache (hết hạn sau 5 phút)
    try:
        await set_cache(cache_key, thread_dict, expire=300)
        logger.info(f"Cached thread '{thread_id}'")
    except Exception as e:
        logger.warning(f"Failed to cache thread {thread_id}: {e}")

    return thread_dict

@router.patch(
    "/threads/{thread_id}",
    response_model=dtos.ThreadResponse,
    summary="Update thread information (e.g., rename)",
    tags=["Threads"]
)
async def update_thread_info_endpoint( # Renamed function
    thread_id: str,
    thread_update: dtos.ThreadUpdate,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    logger.info(f"User '{current_user.identifier}' is updating thread '{thread_id}'")
    # Check ownership before updating
    thread_dict = await get_thread_endpoint(thread_id, db, current_user)

    # Get ID from dict
    thread_id_to_update = thread_dict.get('id')
    if not thread_id_to_update:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Thread ID not found")

    updated_thread = await async_crud.update_thread(db, thread_id=str(thread_id_to_update), thread_update=thread_update)
    if not updated_thread:
        logger.error(f"Could not update thread '{thread_id}' after permission check.")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Could not update thread")

    # Convert SQLAlchemy model to dict
    updated_thread_dict = sqlalchemy_to_dict(updated_thread)
    logger.info(f"Thread '{thread_id}' was successfully updated.")

    # Cập nhật cache
    try:
        cache_key = f"thread:{thread_id}:user:{current_user.id}"
        await set_cache(cache_key, updated_thread_dict, expire=300)
        logger.info(f"Updated cache for thread '{thread_id}'")
    except Exception as e:
        logger.warning(f"Failed to update cache for thread {thread_id}: {e}")

    return updated_thread_dict

@router.delete(
    "/threads/{thread_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a thread",
    tags=["Threads"]
)
async def delete_thread_endpoint( # Renamed function
    thread_id: str,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    logger.info(f"User '{current_user.identifier}' is attempting to delete thread '{thread_id}'")
    db_thread = await async_crud.get_thread(db, thread_id=thread_id)
    if db_thread and str(db_thread.userId) == str(current_user.id):
        success = await async_crud.delete_thread(db=db, thread_id=thread_id)
        if success:
            logger.info(f"Thread '{thread_id}' was successfully deleted by user '{current_user.identifier}'.")
            # Xóa cache nếu có
            try:
                cache_key = f"thread:{thread_id}:user:{current_user.id}"
                await set_cache(cache_key, None, expire=1)  # Expire ngay lập tức
                logger.info(f"Removed thread '{thread_id}' from cache")
            except Exception as e:
                logger.warning(f"Failed to remove thread {thread_id} from cache: {e}")
        else:
            logger.error(f"Thread '{thread_id}' was found but deletion failed.")
    elif db_thread:
         logger.warning(f"User '{current_user.identifier}' is forbidden from deleting thread '{thread_id}'.")
    else:
         logger.warning(f"Attempted to delete non-existent thread '{thread_id}'.")
    return Response(status_code=status.HTTP_204_NO_CONTENT)

# --- Chat Endpoint ---
# Thêm cấu hình để chọn service
USE_RIVALZ = settings.USE_RIVALZ_SERVICE

@router.post(
    "/chat",
    response_model=dtos.ChatResponse,
    summary="Send message and receive AI response",
    tags=["Chat"]
)
async def handle_chat_message_endpoint(
    request: Request,
    request_data: dtos.ChatRequest,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    thread_id = request_data.thread_id
    user_message_content = request_data.message
    logger.info(f"Received chat message from user '{current_user.identifier}' for thread '{thread_id}' with agent_type '{request_data.agent_type}'")

    # Check thread access permission
    await get_thread_endpoint(thread_id, db, current_user)

    # Create user step
    user_step_data = dtos.StepCreate(name="User Message", type="USER_MESSAGE", input=user_message_content)
    try:
        user_step_model = await async_crud.create_step(db=db, step_data=user_step_data, thread_id=thread_id)
        logger.info(f"Saved user message step with ID: {user_step_model.id}")

        # Convert SQLAlchemy model to dict
        user_step_dict = sqlalchemy_to_dict(user_step_model)
        logger.info(f"User step dict after conversion: {user_step_dict}")

        # Create StepResponse from dict
        user_step_response = dtos.StepResponse(**user_step_dict)
    except Exception as e:
        logger.error(f"Error saving user step for thread {thread_id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Could not save user message")

    # Fetch history steps (models from DB)
    history_steps_models = await async_crud.get_last_steps(db, thread_id=thread_id, limit=settings.MAX_HISTORY_MESSAGES)

    generated_ai_steps: List[dtos.StepResponse] = []

    if USE_RIVALZ:
        logger.info("Using Rivalz service for AI response.")
        # Convert history models to list of dicts for formatting
        history_for_prompt_rivalz = []
        for s in history_steps_models:
            step_dict = sqlalchemy_to_dict(s)
            history_for_prompt_rivalz.append(dtos.StepResponse(**step_dict).model_dump())
        chat_history_for_prompt_rivalz = rivalz_service.format_history_for_prompt(history_for_prompt_rivalz)
        logger.info(f"Formatted chat history for Rivalz: {chat_history_for_prompt_rivalz}")
        prompt_messages_rivalz = chat_history_for_prompt_rivalz + [{"role": "user", "content": user_message_content}]

        project_id_header = request.headers.get("project-id")
        if not project_id_header:
            logger.warning("Missing project-id header for Rivalz service")
            # Tạo step lỗi và thêm vào generated_ai_steps thay vì raise HTTPException ngay
            error_step_data = dtos.StepCreate(
                name="Configuration Error",
                type="ERROR",
                output="Missing project-id header required for Rivalz service.",
                parentId=str(user_step_model.id),
                metadata={"error_type": "missing_project_id"}
            )
            db_error_step = await async_crud.create_step(db=db, step_data=error_step_data, thread_id=thread_id)
            step_dict = sqlalchemy_to_dict(db_error_step)
            generated_ai_steps.append(dtos.StepResponse(**step_dict))
        else:
            agent_type_selected = request_data.agent_type # Lấy từ request
            if not agent_type_selected: # Đảm bảo có giá trị, nếu không thì dùng default
                 agent_type_selected = "rx" # Hoặc default từ config
                 logger.info(f"agent_type not provided in request, defaulting to '{agent_type_selected}'")


            agent_response_obj = await rivalz_service.get_rivalz_response(
                user_id=str(current_user.id),
                thread_id=thread_id,
                prompt_messages=prompt_messages_rivalz,
                project_id=project_id_header,
                agent_type_selected=agent_type_selected
            )

            if agent_response_obj:
                # Hiện tại chỉ xử lý non-streaming, nếu có streaming cần logic riêng
                if agent_response_obj.streaming:
                    logger.warning("Streaming response from Rivalz is not fully handled for multi-step generation yet.")
                    # Tạo một step thông báo về streaming
                    streaming_info_step_data = dtos.StepCreate(
                        name="AI Response (Streaming Info)",
                        type="AI_RESPONSE",
                        output="AI is processing a streaming response. Full content will be available once stream completes.",
                        parentId=str(user_step_model.id),
                        metadata={"status": "streaming_not_yet_fully_parsed_to_steps",
                                  "rivalz_metadata": vars(agent_response_obj.metadata).copy() if agent_response_obj.metadata and not hasattr(agent_response_obj.metadata, 'model_dump') else (agent_response_obj.metadata.model_dump(exclude_unset=True) if agent_response_obj.metadata else {})}
                    )
                    db_streaming_info_step = await async_crud.create_step(db=db, step_data=streaming_info_step_data, thread_id=thread_id)
                    step_dict = sqlalchemy_to_dict(db_streaming_info_step)
                    generated_ai_steps.append(dtos.StepResponse(**step_dict))
                else: # Non-streaming Rivalz response
                    extracted_contents_list: Optional[List[str]] = rivalz_service.extract_content_from_response(agent_response_obj)

                    if extracted_contents_list:
                        for index, raw_agent_response_str in enumerate(extracted_contents_list):
                            if not raw_agent_response_str or not raw_agent_response_str.strip():
                                logger.warning(f"Skipping empty raw agent response at index {index} for thread {thread_id}.")
                                continue

                            parsed_agent_name, parsed_content = parse_agent_response(raw_agent_response_str)
                            step_specific_metadata: Dict[str, Any] = {}

                            # if agent_response_obj.metadata:
                            #     if hasattr(agent_response_obj.metadata, 'model_dump'):
                            #         step_specific_metadata = agent_response_obj.metadata.model_dump(exclude_unset=True)
                            #     else:
                            #         step_specific_metadata = vars(agent_response_obj.metadata).copy()

                            step_name: str
                            if parsed_agent_name:
                                step_specific_metadata['source_agent_name'] = parsed_agent_name
                                step_name = f"Response from {parsed_agent_name}"
                            else:
                                step_name = f"AI Response Part {index + 1} (Unknown Source)"
                                step_specific_metadata['source_agent_name'] = "Unknown"
                            ai_step_data = dtos.StepCreate(
                                name=step_name,
                                type="AI_RESPONSE",
                                output=parsed_content,
                                parentId=str(user_step_model.id),
                                metadata=step_specific_metadata
                            )
                            ## log the metadata
                            logger.info(f"Creating AI step for thread {thread_id} with metadata: {step_specific_metadata}")
                            # logs the parsed_content
                            logger.info(f"Creating AI step for thread {thread_id} with content: {parsed_content[:100]}...")  # Log first 100 chars
                            db_ai_step = await async_crud.create_step(db=db, step_data=ai_step_data, thread_id=thread_id)
                            step_dict = sqlalchemy_to_dict(db_ai_step)
                            generated_ai_steps.append(dtos.StepResponse(**step_dict))
                        
                        if not generated_ai_steps and extracted_contents_list: # Có list nhưng không tạo được step nào (ví dụ tất cả rỗng)
                             logger.warning(f"Extracted content list was not empty but no steps were generated for thread {thread_id}.")
                             # Tạo một step thông báo
                             info_step_data = dtos.StepCreate(
                                name="AI Processing Info", type="AI_RESPONSE", output="AI processed the request, but individual parts were empty.",
                                parentId=str(user_step_model.id),
                                metadata={"info": "all_extracted_parts_empty", "rivalz_metadata": vars(agent_response_obj.metadata).copy() if agent_response_obj.metadata and not hasattr(agent_response_obj.metadata, 'model_dump') else (agent_response_obj.metadata.model_dump(exclude_unset=True) if agent_response_obj.metadata else {})}
                             )
                             db_info_step = await async_crud.create_step(db=db, step_data=info_step_data, thread_id=thread_id)
                             step_dict = sqlalchemy_to_dict(db_info_step)
                             generated_ai_steps.append(dtos.StepResponse(**step_dict))

                    elif extracted_contents_list == []: # extract_content_from_response trả về list rỗng
                        logger.info(f"Rivalz: extract_content_from_response returned an empty list for thread {thread_id}.")
                        no_content_output = "Agent processed the request but returned no specific textual parts."
                        no_content_step_data = dtos.StepCreate(
                            name="AI Response Info (Rivalz)", type="AI_RESPONSE", output=no_content_output,
                            parentId=str(user_step_model.id),
                            metadata={"info": "rivalz_empty_extracted_content_list", "rivalz_metadata": vars(agent_response_obj.metadata).copy() if agent_response_obj.metadata and not hasattr(agent_response_obj.metadata, 'model_dump') else (agent_response_obj.metadata.model_dump(exclude_unset=True) if agent_response_obj.metadata else {})}
                        )
                        db_no_content_step = await async_crud.create_step(db=db, step_data=no_content_step_data, thread_id=thread_id)
                        step_dict = sqlalchemy_to_dict(db_no_content_step)
                        generated_ai_steps.append(dtos.StepResponse(**step_dict))
                    else: # extracted_contents_list là None (lỗi trích xuất)
                        logger.error(f"Rivalz: extract_content_from_response returned None for thread {thread_id}.")
                        extraction_error_output = "Sorry, there was an error processing the AI's response format."
                        error_step_data = dtos.StepCreate(
                            name="AI Extraction Error (Rivalz)", type="ERROR", output=extraction_error_output,
                            parentId=str(user_step_model.id),
                            metadata={"error": "rivalz_extraction_returned_none", "rivalz_metadata": vars(agent_response_obj.metadata).copy() if agent_response_obj.metadata and not hasattr(agent_response_obj.metadata, 'model_dump') else (agent_response_obj.metadata.model_dump(exclude_unset=True) if agent_response_obj.metadata else {})}
                        )
                        db_extraction_error_step = await async_crud.create_step(db=db, step_data=error_step_data, thread_id=thread_id)
                        step_dict = sqlalchemy_to_dict(db_extraction_error_step)
                        generated_ai_steps.append(dtos.StepResponse(**step_dict))
            else: # agent_response_obj là None (lỗi từ get_rivalz_response)
                logger.error(f"Rivalz: get_rivalz_response returned None for thread {thread_id}.")
                service_error_output = "Unable to connect to or receive a response from the Rivalz AI service."
                error_step_data = dtos.StepCreate(
                    name="AI Service Error (Rivalz)", type="ERROR", output=service_error_output,
                    parentId=str(user_step_model.id),
                    metadata={"error": "rivalz_service_get_response_returned_none"}
                )
                db_service_error_step = await async_crud.create_step(db=db, step_data=error_step_data, thread_id=thread_id)
                step_dict = sqlalchemy_to_dict(db_service_error_step)
                generated_ai_steps.append(dtos.StepResponse(**step_dict))
    else: # Fallback to ChatGPT service
        logger.info("Using ChatGPT service for AI response.")
        history_for_prompt_gpt = chatgpt_service.format_history_for_prompt(history_steps_models)
        #chat_history_for_prompt_gpt = chatgpt_service.format_history_for_prompt(history_for_prompt_gpt)
        prompt_messages_gpt = history_for_prompt_gpt + [{"role": "user", "content": user_message_content}]

        ai_raw_response_dict = await chatgpt_service.get_chatgpt_response(prompt_messages_gpt)
        
        step_name_gpt = "AI Response (ChatGPT)"
        step_type_gpt = "AI_RESPONSE"
        final_content_gpt = ""
        gpt_metadata: Dict[str, Any] = {}

        if ai_raw_response_dict:
            extracted_gpt_content = chatgpt_service.extract_content_from_response(ai_raw_response_dict)
            if extracted_gpt_content:
                final_content_gpt = extracted_gpt_content
                if "usage" in ai_raw_response_dict:
                    gpt_metadata["openai_response_usage"] = ai_raw_response_dict.get("usage", {})
            else:
                logger.error(f"ChatGPT: Could not extract content from response for thread {thread_id}.")
                final_content_gpt = "Sorry, I received an invalid response format from the AI (ChatGPT)."
                step_type_gpt = "ERROR"
                gpt_metadata["error"] = "chatgpt_extraction_failed"
        else: # ai_raw_response_dict is None
            logger.error(f"ChatGPT: No response received from AI API for thread {thread_id}.")
            final_content_gpt = "Sorry, I cannot connect to the AI (ChatGPT) at this time."
            step_type_gpt = "ERROR"
            gpt_metadata["error"] = "chatgpt_service_no_response"

        gpt_step_data = dtos.StepCreate(
            name=step_name_gpt,
            type=step_type_gpt,
            output=final_content_gpt,
            parentId=str(user_step_model.id),
            metadata=gpt_metadata
        )
        db_gpt_step = await async_crud.create_step(db=db, step_data=gpt_step_data, thread_id=thread_id)
        step_dict = sqlalchemy_to_dict(db_gpt_step)
        generated_ai_steps.append(dtos.StepResponse(**step_dict))

    # Final check: if no AI steps were generated by any service, create a generic error step
    if not generated_ai_steps:
        logger.critical(f"CRITICAL: No AI steps were generated for thread {thread_id} after all processing. This indicates a flaw in the logic.")
        critical_error_output = "A critical error occurred, and no AI response could be processed."
        critical_error_step_data = dtos.StepCreate(
            name="System Processing Error",
            type="ERROR",
            output=critical_error_output,
            parentId=str(user_step_model.id),
            metadata={"error": "no_ai_steps_generated_critical_fallback"}
        )
        db_critical_error_step = await async_crud.create_step(db=db, step_data=critical_error_step_data, thread_id=thread_id)
        step_dict = sqlalchemy_to_dict(db_critical_error_step)
        generated_ai_steps.append(dtos.StepResponse(**step_dict))

    # Determine primary AI response content for the ChatResponse.ai_response field
    # (e.g., from the first successful AI step or a summary)
    ai_responses: List[str] = []
    if generated_ai_steps:
        # Lấy output từ tất cả các AI steps thành công
        for step in generated_ai_steps:
            if step.type == "AI_RESPONSE" and isinstance(step.output, str) and step.output.strip():
                ai_responses.append(step.output)
        
        # Nếu không có AI_RESPONSE nào thành công, lấy từ step đầu tiên nếu có output
        if not ai_responses and generated_ai_steps[0].output and isinstance(generated_ai_steps[0].output, str):
            ai_responses.append(generated_ai_steps[0].output)
        
        # Fallback nếu vẫn không có response nào
        if not ai_responses:
            ai_responses.append("AI response generated, see details in steps.")
    else:
        ai_responses.append("No AI response generated.")

    # Cache invalidation
    try:
        cache_keys_to_invalidate = [
            f"thread:{thread_id}:user:{current_user.id}",
            f"user_threads:{current_user.id}:skip=0:limit=100" # Ví dụ key cache user threads
        ]
        # Thêm các key cache cho steps của thread
        for s_offset in [0, 10, 20]: # settings.DEFAULT_SKIP_STEPS nếu có
            for l_limit in [10, 20, 50, 100]: # settings.DEFAULT_LIMIT_STEPS nếu có
                cache_keys_to_invalidate.append(f"thread_steps:{thread_id}:skip={s_offset}:limit={l_limit}")
        
        for key in cache_keys_to_invalidate:
            await set_cache(key, None, expire=1) # Expire ngay lập tức

        logger.info(f"Invalidated cache for thread '{thread_id}' and related entries.")
    except Exception as e:
        logger.warning(f"Failed to invalidate cache for thread {thread_id}: {e}", exc_info=True)

    # Commit database session
    try:
        await db.commit()
        logger.info(f"Database transaction committed for thread {thread_id}.")
    except Exception as e:
        logger.error(f"Error committing database changes for thread {thread_id}: {e}", exc_info=True)
        # Consider if a rollback is needed or if the error is critical enough to raise 500
        # await db.rollback() # Nếu cần
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to save chat interaction.")

    return dtos.ChatResponse(
        thread_id=thread_id,
        ai_response=ai_responses,
        user_step=user_step_response,
        ai_steps=generated_ai_steps
    )
# --- Feedback Endpoint ---
@router.post(
    "/feedbacks",
    response_model=dtos.FeedbackResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Send feedback for a message (step)",
    tags=["Feedback"]
)
async def create_feedback_endpoint( # Renamed function
    feedback_data: dtos.FeedbackCreate,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    step_id_to_feedback = feedback_data.forId
    logger.info(f"User '{current_user.identifier}' is sending feedback for step '{step_id_to_feedback}'")
    target_step = await async_crud.get_step(db, step_id=step_id_to_feedback)
    if not target_step:
        logger.warning(f"Step '{step_id_to_feedback}' not found for feedback.")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Step for feedback not found")

    db_thread_of_step = await async_crud.get_thread(db, thread_id=str(target_step.threadId))
    if not db_thread_of_step or str(db_thread_of_step.userId) != str(current_user.id):
         logger.warning(f"User '{current_user.identifier}' is forbidden from giving feedback for step '{step_id_to_feedback}'.")
         raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not allowed to give feedback for this step")
    try:
        new_feedback = await async_crud.create_feedback(db=db, feedback_data=feedback_data, thread_id=str(target_step.threadId))
        logger.info(f"Feedback created with ID: {new_feedback.id} for step '{step_id_to_feedback}'")

        # Cập nhật cache cho thread và steps
        try:
            # Xóa cache cho thread
            thread_cache_key = f"thread:{target_step.threadId}:user:{current_user.id}"
            await set_cache(thread_cache_key, None, expire=1)  # Expire ngay lập tức để buộc refresh

            # Xóa cache cho steps với các giá trị skip và limit khác nhau
            for s in [0, 10, 20]:
                for l in [10, 20, 50, 100]:
                    steps_cache_key = f"thread_steps:{target_step.threadId}:skip={s}:limit={l}"
                    await set_cache(steps_cache_key, None, expire=1)  # Expire ngay lập tức để buộc refresh

            logger.info(f"Invalidated cache for thread '{target_step.threadId}' and its steps after feedback")
        except Exception as e:
            logger.warning(f"Failed to invalidate cache for thread {target_step.threadId}: {e}")

        return new_feedback
    except Exception as e:
        logger.error(f"Error creating feedback for step {step_id_to_feedback}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Could not save feedback")

# --- User Endpoint to get current user information ---
@router.get(
    "/users/me",
    response_model=dtos.UserResponse,
    summary="Get current authenticated user information",
    tags=["Users"]
)
async def read_users_me_endpoint(
    request: Request,
    current_user: models.User = Depends(get_current_user_async)
): 
    logger.info(f"Getting information for current project '{current_user.identifier}'")

    # Convert SQLAlchemy model to dict
    user_dict = sqlalchemy_to_dict(current_user)

    # Log for debugging
    logger.info(f"Project data after conversion: {user_dict}")

    return user_dict

# --- Endpoint to get steps in a thread (detailed history) ---
@router.get(
    "/threads/{thread_id}/steps",
    response_model=List[dtos.StepResponse],
    summary="Get list of steps in a thread (detailed history)",
    tags=["Threads"]
)
async def get_thread_steps_endpoint( # Renamed function
    thread_id: str,
    skip: int = 0,
    limit: int = 100, # Can increase default limit if needed
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    logger.info(f"Getting steps for thread '{thread_id}' by user '{current_user.identifier}'")

    # Tạo cache key
    cache_key = f"thread_steps:{thread_id}:skip={skip}:limit={limit}"

    # Biến để kiểm soát việc sử dụng cache
    use_cache = True

    # Thử lấy từ cache
    cached_data = None
    try:
        cached_data = await get_cache(cache_key)
        if cached_data and len(cached_data) > 0:
            logger.info(f"Retrieved {len(cached_data)} steps from cache for thread '{thread_id}'")
    except Exception as e:
        logger.warning(f"Failed to get steps from cache: {e}")
        use_cache = False

    # Check thread access permission first
    await get_thread_endpoint(thread_id, db, current_user)

    # Nếu không có trong cache hoặc không sử dụng cache, lấy từ database
    if not cached_data or not use_cache:
        steps = await async_crud.get_steps_by_thread(db, thread_id=thread_id, skip=skip, limit=limit)

        # Convert list of SQLAlchemy models to dicts
        step_dicts = [sqlalchemy_to_dict(step) for step in steps]

        # Thêm thông tin feedback (like/dislike) cho mỗi step
        for step_dict in step_dicts:
            step_id = step_dict["id"]
            feedback_values = await async_crud.get_feedback_value_by_step_id(db, step_id)
            step_dict["like"] = feedback_values["like"]
            step_dict["dislike"] = feedback_values["dislike"]

        logger.info(f"Retrieved {len(step_dicts)} steps with feedback for thread '{thread_id}' from database")

        # Lưu vào cache (hết hạn sau 1 phút)
        if use_cache:
            try:
                await set_cache(cache_key, step_dicts, expire=60)
                logger.info(f"Cached {len(step_dicts)} steps for thread '{thread_id}'")
            except Exception as e:
                logger.warning(f"Failed to cache steps for thread {thread_id}: {e}")

        return step_dicts

    # Nếu dữ liệu từ cache không có trường like/dislike, thêm vào
    if cached_data and (not all(("like" in step and "dislike" in step) for step in cached_data)):
        logger.info(f"Adding missing feedback data to cached steps for thread '{thread_id}'")
        for step_dict in cached_data:
            if "like" not in step_dict or "dislike" not in step_dict:
                step_id = step_dict["id"]
                feedback_values = await async_crud.get_feedback_value_by_step_id(db, step_id)
                step_dict["like"] = feedback_values["like"]
                step_dict["dislike"] = feedback_values["dislike"]

        # Cập nhật cache với dữ liệu mới
        try:
            await set_cache(cache_key, cached_data, expire=60)
            logger.info(f"Updated cache with feedback data for thread '{thread_id}'")
        except Exception as e:
            logger.warning(f"Failed to update cache with feedback data for thread {thread_id}: {e}")

    return cached_data
