"""
Chat routes for handling chat messages and AI responses.
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
import logging

from .. import dtos
from ..db import models, async_crud
from ..db.async_database import get_async_db_dependency
from ..middleware.async_security import get_current_user_async
from ..services import rivalz_service
from config import settings
from .chat_helpers import process_rivalz_content, handle_chatgpt_response, invalidate_cache

# Configure logging
logger = logging.getLogger(__name__)

# Create router for chat endpoints
router = APIRouter()

# Helper function to convert SQLAlchemy model to dict
def sqlalchemy_to_dict(obj) -> Dict[str, Any]:
    """Convert SQLAlchemy model to dict, handling special fields."""
    import json
    import datetime
    from sqlalchemy import MetaData

    result = {}
    for key in obj.__dict__:
        if key.startswith('_'):
            continue
        value = getattr(obj, key)

        # Handle special fields
        if key == 'metadata_':
            # Ensure metadata is a dict
            try:
                if value is None:
                    result['metadata'] = {}
                elif isinstance(value, dict):
                    result['metadata'] = dict(value)
                elif isinstance(value, MetaData):
                    # Handle SQLAlchemy MetaData objects
                    result['metadata'] = {}
                elif hasattr(value, '__dict__'):
                    # If it's an object with __dict__, convert to dict
                    try:
                        result['metadata'] = {k: v for k, v in value.__dict__.items() if not k.startswith('_')}
                    except:
                        result['metadata'] = {}
                else:
                    # Try to convert to dict if it's a JSON string
                    try:
                        if isinstance(value, str):
                            result['metadata'] = json.loads(value)
                        else:
                            result['metadata'] = {}
                    except:
                        result['metadata'] = {}
            except:
                result['metadata'] = {}
        elif key == 'tags':
            # Ensure tags is a list
            try:
                if value is None:
                    result['tags'] = []
                elif isinstance(value, list):
                    result['tags'] = list(value)
                elif hasattr(value, '__iter__') and not isinstance(value, (str, dict)):
                    # If it's iterable but not a string or dict
                    result['tags'] = list(value)
                else:
                    # Try to convert to list if it's a JSON string
                    try:
                        if isinstance(value, str):
                            result['tags'] = json.loads(value)
                        else:
                            result['tags'] = []
                    except:
                        result['tags'] = []
            except:
                result['tags'] = []
        elif isinstance(value, datetime.datetime):
            # Convert datetime to ISO format string
            result[key] = value.isoformat()
        elif isinstance(value, (datetime.date, datetime.time)):
            # Convert date and time to ISO format string
            result[key] = value.isoformat()
        else:
            result[key] = value

    return result

# Configuration for service selection
USE_RIVALZ = settings.USE_RIVALZ_SERVICE

@router.post(
    "/chat",
    response_model=dtos.ChatResponse,
    summary="Send message and receive AI response",
    tags=["Chat"]
)
async def handle_chat_message(
    request: Request,
    request_data: dtos.ChatRequest,
    db: AsyncSession = Depends(get_async_db_dependency),
    current_user: models.User = Depends(get_current_user_async)
):
    """
    Handle chat message and return AI response.
    
    This endpoint processes user messages and returns AI responses using either
    Rivalz service or ChatGPT service based on configuration.
    """
    thread_id = request_data.thread_id
    user_message_content = request_data.message
    logger.info(f"Received chat message from user '{current_user.identifier}' for thread '{thread_id}' with agent_type '{request_data.agent_type}'")

    # Check thread access permission
    from .thread_routes import get_thread_endpoint
    await get_thread_endpoint(thread_id, db, current_user)

    # Create user step
    user_step_data = dtos.StepCreate(name="User Message", type="USER_MESSAGE", input=user_message_content)
    try:
        user_step_model = await async_crud.create_step(db=db, step_data=user_step_data, thread_id=thread_id)
        logger.info(f"Saved user message step with ID: {user_step_model.id}")

        # Convert SQLAlchemy model to dict
        user_step_dict = sqlalchemy_to_dict(user_step_model)
        logger.info(f"User step dict after conversion: {user_step_dict}")

        # Create StepResponse from dict
        user_step_response = dtos.StepResponse(**user_step_dict)
    except Exception as e:
        logger.error(f"Error saving user step for thread {thread_id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Could not save user message")

    # Fetch history steps (models from DB)
    history_steps_models = await async_crud.get_last_steps(db, thread_id=thread_id, limit=settings.MAX_HISTORY_MESSAGES)

    generated_ai_steps: List[dtos.StepResponse] = []

    if USE_RIVALZ:
        generated_ai_steps = await handle_rivalz_response(
            request, user_message_content, user_step_model, history_steps_models, 
            thread_id, request_data, db, current_user
        )
    else:
        generated_ai_steps = await handle_chatgpt_response(
            user_message_content, user_step_model, history_steps_models,
            thread_id, db, current_user
        )

    # Final check: if no AI steps were generated by any service, create a generic error step
    if not generated_ai_steps:
        logger.critical(f"CRITICAL: No AI steps were generated for thread {thread_id} after all processing. This indicates a flaw in the logic.")
        critical_error_output = "A critical error occurred, and no AI response could be processed."
        critical_error_step_data = dtos.StepCreate(
            name="System Processing Error",
            type="ERROR",
            output=critical_error_output,
            parentId=str(user_step_model.id),
            metadata={"error": "no_ai_steps_generated_critical_fallback"}
        )
        db_critical_error_step = await async_crud.create_step(db=db, step_data=critical_error_step_data, thread_id=thread_id)
        step_dict = sqlalchemy_to_dict(db_critical_error_step)
        generated_ai_steps.append(dtos.StepResponse(**step_dict))

    # Determine primary AI response content for the ChatResponse.ai_response field
    ai_responses: List[str] = []
    if generated_ai_steps:
        # Lấy output từ tất cả các AI steps thành công
        for step in generated_ai_steps:
            if step.type == "AI_RESPONSE" and isinstance(step.output, str) and step.output.strip():
                ai_responses.append(step.output)
        
        # Nếu không có AI_RESPONSE nào thành công, lấy từ step đầu tiên nếu có output
        if not ai_responses and generated_ai_steps[0].output and isinstance(generated_ai_steps[0].output, str):
            ai_responses.append(generated_ai_steps[0].output)
        
        # Fallback nếu vẫn không có response nào
        if not ai_responses:
            ai_responses.append("AI response generated, see details in steps.")
    else:
        ai_responses.append("No AI response generated.")

    # Cache invalidation
    await invalidate_cache(thread_id, str(current_user.id))

    # Commit database session
    try:
        await db.commit()
        logger.info(f"Database transaction committed for thread {thread_id}.")
    except Exception as e:
        logger.error(f"Error committing database changes for thread {thread_id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to save chat interaction.")

    return dtos.ChatResponse(
        thread_id=thread_id,
        ai_response=ai_responses,
        user_step=user_step_response,
        ai_steps=generated_ai_steps
    )

async def handle_rivalz_response(
    request: Request,
    user_message_content: str,
    user_step_model: models.Step,
    history_steps_models: List[models.Step],
    thread_id: str,
    request_data: dtos.ChatRequest,
    db: AsyncSession,
    current_user: models.User
) -> List[dtos.StepResponse]:
    """Handle Rivalz service response processing."""
    logger.info("Using Rivalz service for AI response.")
    generated_ai_steps: List[dtos.StepResponse] = []

    # Convert history models to list of dicts for formatting
    history_for_prompt_rivalz = []
    for s in history_steps_models:
        step_dict = sqlalchemy_to_dict(s)
        history_for_prompt_rivalz.append(dtos.StepResponse(**step_dict).model_dump())

    chat_history_for_prompt_rivalz = rivalz_service.format_history_for_prompt(history_for_prompt_rivalz)
    logger.info(f"Formatted chat history for Rivalz: {chat_history_for_prompt_rivalz}")
    prompt_messages_rivalz = chat_history_for_prompt_rivalz + [{"role": "user", "content": user_message_content}]

    project_id_header = request.headers.get("project-id")
    if not project_id_header:
        logger.warning("Missing project-id header for Rivalz service")
        # Tạo step lỗi và thêm vào generated_ai_steps
        error_step_data = dtos.StepCreate(
            name="Configuration Error",
            type="ERROR",
            output="Missing project-id header required for Rivalz service.",
            parentId=str(user_step_model.id),
            metadata={"error_type": "missing_project_id"}
        )
        db_error_step = await async_crud.create_step(db=db, step_data=error_step_data, thread_id=thread_id)
        step_dict = sqlalchemy_to_dict(db_error_step)
        generated_ai_steps.append(dtos.StepResponse(**step_dict))
        return generated_ai_steps

    agent_type_selected = request_data.agent_type
    if not agent_type_selected:
        agent_type_selected = "rx"  # Default
        logger.info(f"agent_type not provided in request, defaulting to '{agent_type_selected}'")

    agent_response_obj = await rivalz_service.get_rivalz_response(
        user_id=str(current_user.id),
        thread_id=thread_id,
        prompt_messages=prompt_messages_rivalz,
        project_id=project_id_header,
        agent_type_selected=agent_type_selected
    )

    if agent_response_obj:
        if agent_response_obj.streaming:
            logger.warning("Streaming response from Rivalz is not fully handled for multi-step generation yet.")
            # Tạo một step thông báo về streaming
            streaming_info_step_data = dtos.StepCreate(
                name="AI Response (Streaming Info)",
                type="AI_RESPONSE",
                output="AI is processing a streaming response. Full content will be available once stream completes.",
                parentId=str(user_step_model.id),
                metadata={"status": "streaming_not_yet_fully_parsed_to_steps"}
            )
            db_streaming_info_step = await async_crud.create_step(db=db, step_data=streaming_info_step_data, thread_id=thread_id)
            step_dict = sqlalchemy_to_dict(db_streaming_info_step)
            generated_ai_steps.append(dtos.StepResponse(**step_dict))
        else:
            # Non-streaming Rivalz response
            extracted_contents_list: Optional[List[str]] = rivalz_service.extract_content_from_response(agent_response_obj)
            generated_ai_steps = await process_rivalz_content(
                extracted_contents_list, agent_response_obj, user_step_model, thread_id, db
            )
    else:
        # agent_response_obj là None (lỗi từ get_rivalz_response)
        logger.error(f"Rivalz: get_rivalz_response returned None for thread {thread_id}.")
        service_error_output = "Unable to connect to or receive a response from the Rivalz AI service."
        error_step_data = dtos.StepCreate(
            name="AI Service Error (Rivalz)", type="ERROR", output=service_error_output,
            parentId=str(user_step_model.id),
            metadata={"error": "rivalz_service_get_response_returned_none"}
        )
        db_service_error_step = await async_crud.create_step(db=db, step_data=error_step_data, thread_id=thread_id)
        step_dict = sqlalchemy_to_dict(db_service_error_step)
        generated_ai_steps.append(dtos.StepResponse(**step_dict))

    return generated_ai_steps
