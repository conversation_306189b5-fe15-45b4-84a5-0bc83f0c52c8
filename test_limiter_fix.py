#!/usr/bin/env python3
"""Test FastAPI Limiter fix"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, '.')

async def test_limiter_fix():
    """Test the FastAPI Limiter fix"""
    print("🔄 Testing FastAPI Limiter fix...")
    
    try:
        # Test import
        print("🔄 Testing imports...")
        from app.middleware.cache import setup_redis, _safe_limiter_call
        print("✅ Cache module imported successfully")
        
        # Test FastAPI Limiter import
        try:
            from fastapi_limiter import FastAPILimiter
            print("✅ FastAPILimiter imported successfully")
            
            # Test safe limiter call with mock operations
            print("🔄 Testing _safe_limiter_call...")
            
            # Test with string return (common case)
            result1 = await _safe_limiter_call("test_string")
            print(f"✅ Safe limiter call with string: {result1}")
            
            # Test with None return
            result2 = await _safe_limiter_call(None)
            print(f"✅ Safe limiter call with None: {result2}")
            
            # Test with coroutine (if available)
            async def mock_coroutine():
                return "coroutine_result"
            
            result3 = await _safe_limiter_call(mock_coroutine())
            print(f"✅ Safe limiter call with coroutine: {result3}")
            
        except ImportError as e:
            print(f"⚠️ FastAPILimiter not available: {e}")
            print("💡 Try: pip install slowapi")
        
        # Test full setup
        print("\n🔄 Testing full Redis setup...")
        try:
            await asyncio.wait_for(setup_redis(), timeout=10.0)
            print("✅ Redis setup completed successfully")
        except asyncio.TimeoutError:
            print("⚠️ Redis setup timed out - this is expected if Redis is not running")
        except Exception as e:
            print(f"⚠️ Redis setup failed: {e}")
            print("This is expected if Redis is not installed or running")
        
        print("\n✅ FastAPI Limiter fix test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_limiter_fix())
