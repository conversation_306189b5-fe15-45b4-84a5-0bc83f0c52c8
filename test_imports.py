"""
Simple script to test imports.
"""

import sys
import os

print(f"Current directory: {os.getcwd()}")
print(f"Python path: {sys.path}")

try:
    from app.services.rivalz_service import extract_content_from_response
    print("Successfully imported extract_content_from_response")
except ImportError as e:
    print(f"Error importing extract_content_from_response: {e}")

try:
    from app.services.utils import clean_text
    print("Successfully imported clean_text")
except ImportError as e:
    print(f"Error importing clean_text: {e}")

print("Import test completed")
