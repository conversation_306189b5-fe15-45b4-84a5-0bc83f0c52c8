# gunicorn_conf.py
import multiprocessing
import os

# Môi trường
environment = os.getenv("ENVIRONMENT", "production")
debug = environment == "development"

# C<PERSON>u hình worker
# Số lượng worker processes đ<PERSON><PERSON><PERSON> nghị là (2 x số lõi CPU) + 1
workers_per_core = float(os.getenv("WORKERS_PER_CORE", "1"))
max_workers = int(os.getenv("MAX_WORKERS", "0"))
cores = multiprocessing.cpu_count()
default_workers = (cores * 2) + 1
workers = max_workers if max_workers > 0 else int(cores * workers_per_core) + 1
workers = min(default_workers, workers)

# Cấu hình worker class
# 'uvicorn.workers.UvicornWorker' là worker class tốt nhất cho FastAPI
worker_class = "uvicorn.workers.UvicornWorker"

# Cấu hình timeout
timeout = int(os.getenv("TIMEOUT", "120"))  # 2 phút
graceful_timeout = int(os.getenv("GRACEFUL_TIMEOUT", "120"))  # 2 phút
keepalive = int(os.getenv("KEEP_ALIVE", "5"))  # 5 giây

# Cấu hình binding
host = os.getenv("HOST", "0.0.0.0")
port = os.getenv("PORT", "8000")
bind = f"{host}:{port}"

# Cấu hình logging
accesslog = os.getenv("ACCESS_LOG", "-")  # "-" để log ra stdout
errorlog = os.getenv("ERROR_LOG", "-")  # "-" để log ra stderr
loglevel = os.getenv("LOG_LEVEL", "info")

# Cấu hình worker connections
# Số lượng kết nối đồng thời tối đa mà mỗi worker có thể xử lý
worker_connections = int(os.getenv("WORKER_CONNECTIONS", "1000"))

# Cấu hình reload (chỉ cho development)
reload = debug
reload_extra_files = []

# Cấu hình SSL (nếu cần)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# Cấu hình tiến trình
proc_name = "fastapi_app"
daemon = False

# Cấu hình bảo mật
limit_request_line = int(os.getenv("LIMIT_REQUEST_LINE", "4094"))  # Giới hạn độ dài của request line
limit_request_fields = int(os.getenv("LIMIT_REQUEST_FIELDS", "100"))  # Giới hạn số lượng header fields

# In thông tin cấu hình
print(f"Running with {workers} workers, binding to {bind}")
