import asyncio
from sqlalchemy import text
from db.async_database import init_async_db, get_async_db_dependency

async def main():
    print("Creating database tables asynchronously...")
    await init_async_db()

    # <PERSON><PERSON><PERSON> tra kết nối
    print("Testing database connection...")
    async for session in get_async_db_dependency():
        try:
            # Thực hiện một truy vấn đơn giản với text() để khai báo rõ ràng SQL
            result = await session.execute(text("SELECT 1"))
            print(f"Connection test successful: {result.scalar()}")
            break
        except Exception as e:
            print(f"Connection test failed: {e}")
            # <PERSON><PERSON><PERSON><PERSON> raise lỗi để script có thể tiếp tục
            print("Continuing despite connection test failure...")
            break

    print("Database tables created successfully!")

if __name__ == "__main__":
    asyncio.run(main())
