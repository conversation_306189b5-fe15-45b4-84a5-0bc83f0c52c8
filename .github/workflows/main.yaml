name: ragent-be pipeline

on:
  push:
    branches:
      - main
      - staging
      - testnet

# Prevent concurrent deployments
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  REPO_URL: "asia-southeast1-docker.pkg.dev/rivalz-be/rome"
  SERVICE_NAME: "ragent-be"

jobs:
  build-and-push:
    runs-on: rivalz-dc
    outputs:
      commit_hash: ${{ steps.build.outputs.commit_hash }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Build api service
        id: build
        run: |
          commit_hash=$(git rev-parse --short HEAD)
          docker build -t ${REPO_URL}/${SERVICE_NAME}:${commit_hash} -t ${REPO_URL}/${SERVICE_NAME}:latest .
          echo "${{ secrets.SERVICE_ACCOUNT_JSON }}" | base64 -d | docker login -u _json_key --password-stdin asia-southeast1-docker.pkg.dev
          docker push ${REPO_URL}/${SERVICE_NAME}:${commit_hash}
          docker push ${REPO_URL}/${SERVICE_NAME}:latest
          echo "commit_hash=${commit_hash}" >> $GITHUB_OUTPUT

  deploy:
    needs: build-and-push
    runs-on: rivalz-dc
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          repository: "Rivalz-ai/rivalz-helm-chart.git"
          path: rivalz-chart
          ref: main

      - name: Deploy service
        run: |
          NAMESPACE="ragent"
          HOST="api-orchestration.rivalz.ai"
          if [[ ${{ github.ref_name }} == "staging" ]]; then
            HOST="staging-$HOST"
            NAMESPACE="staging-${NAMESPACE}"
          fi
          if [[ ${{ github.ref_name }} == "testnet" ]]; then
            HOST="testnet-$HOST"
            NAMESPACE="testnet-${NAMESPACE}"
          fi
          echo "${{ secrets.DC_KUBECONFIG }}" | base64 -d >> kubeconfig
          helm upgrade --install ${SERVICE_NAME} rivalz-chart -f values.yaml -n ${NAMESPACE} \
            --kubeconfig kubeconfig \
            --set image.tag=${{ needs.build-and-push.outputs.commit_hash }} \
            --set ingress.hosts[0]=${HOST} \
            --create-namespace

