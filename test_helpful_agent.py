#!/usr/bin/env python3
"""Test script for updated helpful agent"""

try:
    from services.chatgpt_service import format_history_for_prompt, HELPFUL_AGENT_SYSTEM_PROMPT
    print("✅ Updated chatgpt_service imported successfully!")
    
    # Test system prompt content
    system_prompt = HELPFUL_AGENT_SYSTEM_PROMPT[0]
    print(f"✅ System prompt role: {system_prompt['role']}")
    print(f"✅ Content includes 'Helpful Agent': {'Helpful Agent' in system_prompt['content']}")
    print(f"✅ Content includes 'versatile': {'versatile' in system_prompt['content']}")
    print(f"✅ No longer healthcare specific: {'health' not in system_prompt['content'].lower()}")
    
    # Test format_history_for_prompt
    result = format_history_for_prompt([])
    print(f"✅ Format function works: {len(result)} items")
    print(f"✅ First item is system prompt: {result[0]['role'] == 'system'}")
    
    print("✅ Helpful Agent system prompt updated successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
